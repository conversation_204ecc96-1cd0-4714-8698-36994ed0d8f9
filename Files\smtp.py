import smtplib 
import ssl 
from email .mime .text import MIMEText 
from email .mime .multipart import MIMEMultipart 
from email .utils import formataddr 
from email .header import Header 
import os ,sys ,ctypes ,logging ,json ,time ,threading ,queue ,urllib .request ,socket 
try :
    import socks 
except ImportError :
    socks =None 


class SocksSMTP (smtplib .SMTP ):
    """
    Custom SMTP class that supports SOCKS5 proxy connections using PySocks.
    This provides a cleaner and more reliable way to handle SOCKS proxy connections.
    """

    def __init__ (self ,
    host ='',
    port =0 ,
    local_hostname =None ,
    timeout =socket ._GLOBAL_DEFAULT_TIMEOUT ,
    source_address =None ,
    proxy_type =None ,
    proxy_addr =None ,
    proxy_port =None ,
    proxy_rdns =True ,
    proxy_username =None ,
    proxy_password =None ,
    socket_options =None ):

        self .proxy_type =proxy_type 
        self .proxy_addr =proxy_addr 
        self .proxy_port =proxy_port 
        self .proxy_rdns =proxy_rdns 
        self .proxy_username =proxy_username 
        self .proxy_password =proxy_password 
        self .socket_options =socket_options 



        if self .proxy_type :
            self ._get_socket =self .socks_get_socket 

        super (SocksSMTP ,self ).__init__ (host ,port ,local_hostname ,timeout ,source_address )

    def socks_get_socket (self ,host ,port ,timeout ):
        """
        Create a socket connection through SOCKS proxy using PySocks create_connection.
        Enhanced with better timeout handling and socket options for SMTP.
        """
        if self .debuglevel >0 :
            self ._print_debug ('connect: to',(host ,port ),self .source_address )


        socket_options =[
        (socket .SOL_SOCKET ,socket .SO_KEEPALIVE ,1 ),
        (socket .IPPROTO_TCP ,socket .TCP_NODELAY ,1 ),
        ]


        if self .socket_options :
            socket_options .extend (self .socket_options )

        try :

            sock =socks .create_connection (
            (host ,port ),
            timeout =max (timeout ,15 ),
            source_address =self .source_address ,
            proxy_type =self .proxy_type ,
            proxy_addr =self .proxy_addr ,
            proxy_port =self .proxy_port ,
            proxy_rdns =self .proxy_rdns ,
            proxy_username =self .proxy_username ,
            proxy_password =self .proxy_password ,
            socket_options =socket_options 
            )


            sock .setsockopt (socket .SOL_SOCKET ,socket .SO_RCVTIMEO ,
            int (timeout *1000 ).to_bytes (4 ,'little')+b'\x00'*4 )
            sock .setsockopt (socket .SOL_SOCKET ,socket .SO_SNDTIMEO ,
            int (timeout *1000 ).to_bytes (4 ,'little')+b'\x00'*4 )

            return sock 

        except Exception as e :
            if self .debuglevel >0 :
                self ._print_debug ('SOCKS connection failed:',str (e ))
            raise 


from time import sleep 
from functools import partial 
from PyQt6 .QtWidgets import QApplication ,QDialog ,QVBoxLayout ,QHBoxLayout ,QMainWindow ,QPlainTextEdit ,QVBoxLayout ,QPushButton ,QMessageBox ,QGroupBox ,QCheckBox ,QLabel ,QSpinBox 
from PyQt6 .QtWebEngineWidgets import QWebEngineView 
from PyQt6 .QtWebEngineCore import QWebEngineSettings 
from PyQt6 .QtCore import QUrl ,Qt ,QObject ,pyqtSignal ,QThread ,QTimer 
from PyQt6 import uic ,QtCore 
from PyQt6 .QtGui import QIcon 


myappid ='mycompany.myproduct.subproduct.version'
ctypes .windll .shell32 .SetCurrentProcessExplicitAppUserModelID (myappid )
home =os .path .dirname (os .path .realpath (__file__ )).replace ("\\","/")


class SMTPConnectionManager :
    """
    Context manager for SMTP connections with connection limiting and proper cleanup.
    """

    def __init__ (self ,proxy_manager ,smtp_host ,smtp_port ,proxy_config =None ):
        self .proxy_manager =proxy_manager
        self .smtp_host =smtp_host
        self .smtp_port =smtp_port
        self .proxy_config =proxy_config
        self .smtp =None
        self .semaphore =None
        self .logger =logging .getLogger ("SMTPConnectionManager")

    def __enter__ (self ):
        """
        Enter the context manager and create SMTP connection with proper limiting.
        """
        if self .proxy_manager .use_global_proxy and self .proxy_manager .global_proxy_active :
            self .smtp =self .proxy_manager ._create_global_proxy_smtp_connection (
                self .smtp_host ,self .smtp_port ,timeout =15
            )
            return self .smtp

        if self .proxy_config is None :
            self .proxy_config =self .proxy_manager .get_current_proxy ()

        if not self .proxy_config :
            self .logger .error ("No proxy configured - direct SMTP connections disabled for security")
            return None

        # Get and acquire semaphore for connection limiting
        self .semaphore =self .proxy_manager ._get_connection_semaphore (self .proxy_config )
        self .logger .info (f"Acquiring connection semaphore for proxy {self.proxy_config['host']}:{self.proxy_config['port']} (max: {self.proxy_manager.max_concurrent_connections})")
        self .semaphore .acquire ()
        self .logger .debug (f"Connection semaphore acquired for proxy {self.proxy_config['host']}:{self.proxy_config['port']}")

        try :
            # Apply connection delay to prevent rapid successive connections
            self .proxy_manager ._apply_connection_delay (self .proxy_config )

            # Create the SMTP connection
            self .smtp =self .proxy_manager .configure_smtp_with_proxy (
                self .smtp_host ,self .smtp_port ,self .proxy_config
            )
            return self .smtp

        except Exception as e :
            # If connection fails, release semaphore immediately
            if self .semaphore :
                self .semaphore .release ()
                self .logger .debug (f"Released connection semaphore due to error: {str(e)}")
            raise

    def __exit__ (self ,exc_type ,exc_val ,exc_tb ):
        """
        Exit the context manager and clean up resources.
        """
        # Close SMTP connection if it exists
        if self .smtp :
            try :
                self .smtp .quit ()
            except Exception as e :
                self .logger .debug (f"Error closing SMTP connection: {str(e)}")
                try :
                    self .smtp .close ()
                except Exception :
                    pass

        # Release semaphore if it was acquired
        if self .semaphore :
            self .semaphore .release ()
            self .logger .info (f"Released connection semaphore for proxy {self.proxy_config['host']}:{self.proxy_config['port']}")


class DirectSMTPConnectionManager :
    """
    Simple context manager for direct SMTP connections (without proxy).
    """

    def __init__ (self ,smtp_host ,smtp_port ):
        self .smtp_host =smtp_host
        self .smtp_port =smtp_port
        self .smtp =None
        self .logger =logging .getLogger ("DirectSMTPConnectionManager")

    def __enter__ (self ):
        """
        Create direct SMTP connection.
        """
        try :
            self .smtp =smtplib .SMTP (self .smtp_host ,self .smtp_port )
            return self .smtp
        except Exception as e :
            self .logger .error (f"Failed to create direct SMTP connection: {str(e)}")
            return None

    def __exit__ (self ,exc_type ,exc_val ,exc_tb ):
        """
        Clean up SMTP connection.
        """
        if self .smtp :
            try :
                self .smtp .quit ()
            except Exception as e :
                self .logger .debug (f"Error closing direct SMTP connection: {str(e)}")
                try :
                    self .smtp .close ()
                except Exception :
                    pass


class ProxyManager :
    """
    A class to manage HTTP/SOCKS proxy connections for email sending.
    This class provides methods to configure, rotate, and manage proxy connections.
    """

    def __init__ (self ,emails_per_proxy =5 ,use_global_proxy =False ,max_concurrent_connections =4 ):
        """
        Initialize the proxy manager.

        Args:
            emails_per_proxy (int): Number of emails to send before rotating to next proxy
            use_global_proxy (bool): Whether to use global socket monkey-patching
            max_concurrent_connections (int): Maximum concurrent SMTP connections per proxy
        """
        self .emails_per_proxy =emails_per_proxy
        self .proxies =[]
        self .current_proxy_index =0
        self .emails_sent_with_current_proxy =0
        self .proxy_lock =threading .Lock ()
        self .logger =logging .getLogger ("ProxyManager")

        # Connection management
        self .max_concurrent_connections =max_concurrent_connections
        self .connection_semaphores ={}  # Per-proxy semaphores
        self .connection_delays ={}  # Per-proxy connection delays
        self .last_connection_time ={}  # Track last connection time per proxy

        # Thread-safe proxy distribution
        self .thread_proxy_assignments ={}  # Thread ID -> proxy index mapping
        self .proxy_assignment_lock =threading .Lock ()
        self .next_proxy_index =0  # For round-robin assignment

        # Proxy health tracking
        self .proxy_failure_counts ={}  # Track failures per proxy
        self .proxy_blacklist =set ()  # Temporarily blacklisted proxies
        self .proxy_last_failure_time ={}  # Track when proxy last failed
        self .blacklist_duration =300  # 5 minutes blacklist duration

        self .use_global_proxy =use_global_proxy
        self .original_socket =None
        self .global_proxy_active =False


        self .logger .setLevel (logging .DEBUG )
        if not self .logger .handlers :
            handler =logging .FileHandler (f"{home}/proxy_manager.log")
            formatter =logging .Formatter ('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler .setFormatter (formatter )
            self .logger .addHandler (handler )

    def load_proxies_from_file (self ,filename ):
        """
        Load proxy configurations from a file.

        Expected format: protocol://host:port:username:password (one per line)
        Example: http://proxy.example.com:8080:user:pass
                socks5://proxy.example.com:1080:user:pass

        Args:
            filename (str): Path to the proxy configuration file
        """
        try :
            self .proxies =[]
            with open (filename ,'r')as f :
                for line in f :
                    line =line .strip ()
                    if line and not line .startswith ('#'):
                        proxy_config =self .parse_proxy_string (line )
                        if proxy_config :
                            self .proxies .append (proxy_config )

            self .logger .info (f"Loaded {len(self.proxies)} proxies from {filename}")
            return True 
        except Exception as e :
            self .logger .error (f"Failed to load proxies from {filename}: {str(e)}")
            return False 

    def add_proxy (self ,proxy_string ):
        """
        Add a single proxy to the proxy list.

        Args:
            proxy_string (str): Proxy string in format protocol://host:port:username:password

        Returns:
            bool: True if proxy was added successfully
        """
        proxy_config =self .parse_proxy_string (proxy_string )
        if proxy_config :
            self .proxies .append (proxy_config )
            self .logger .info (f"Added proxy: {proxy_config['host']}:{proxy_config['port']}")
            return True 
        return False 

    def parse_proxy_string (self ,proxy_string ):
        """
        Parse a proxy string into a configuration dictionary.

        Args:
            proxy_string (str): Proxy string in format protocol://host:port:username:password

        Returns:
            dict: Proxy configuration or None if invalid
        """
        try :

            if '://'not in proxy_string :
                return None 

            protocol ,rest =proxy_string .split ('://',1 )
            protocol =protocol .lower ()

            if protocol not in ['http','https','socks4','socks5']:
                return None 


            parts =rest .split (':')
            if len (parts )<2 :
                return None 

            host =parts [0 ]
            port =int (parts [1 ])
            username =parts [2 ]if len (parts )>2 else None 
            password =parts [3 ]if len (parts )>3 else None 

            return {
            'protocol':protocol ,
            'host':host ,
            'port':port ,
            'username':username ,
            'password':password 
            }
        except Exception as e :
            self .logger .error (f"Failed to parse proxy string '{proxy_string}': {str(e)}")
            return None 

    def get_current_proxy (self ):
        """
        Get the current proxy configuration for the calling thread.
        Uses thread-safe proxy distribution to ensure different threads use different proxies.

        Returns:
            dict: Current proxy configuration or None if no proxies available
        """
        return self .get_proxy_for_thread ()

    def get_proxy_for_thread (self ,thread_id =None ):
        """
        Get a proxy assigned to the current thread using round-robin distribution.
        Each thread gets its own proxy to avoid all threads using the same proxy.

        Args:
            thread_id: Optional thread identifier (uses current thread if None)

        Returns:
            dict: Proxy configuration for this thread or None if no proxies available
        """
        if thread_id is None :
            thread_id =threading .current_thread ().ident

        with self .proxy_assignment_lock :
            if not self .proxies :
                return None

            # Remove blacklisted proxies that have expired
            self ._cleanup_expired_blacklist ()

            # Get available (non-blacklisted) proxies
            available_proxies =self ._get_available_proxies ()
            if not available_proxies :
                self .logger .warning ("All proxies are blacklisted, using original list")
                available_proxies =list (range (len (self .proxies )))

            # Check if thread already has a proxy assigned
            if thread_id in self .thread_proxy_assignments :
                assigned_index =self .thread_proxy_assignments [thread_id ]
                # Verify the assigned proxy is still available and not blacklisted
                if assigned_index in available_proxies :
                    proxy =self .proxies [assigned_index ]
                    self .logger .debug (f"Thread {thread_id} using assigned proxy {proxy['host']}:{proxy['port']}")
                    return proxy
                else :
                    # Assigned proxy is blacklisted, need to reassign
                    self .logger .info (f"Thread {thread_id} proxy is blacklisted, reassigning")
                    del self .thread_proxy_assignments [thread_id ]

            # Assign a new proxy using round-robin from available proxies
            if available_proxies :
                # Find next available proxy index
                attempts =0
                while attempts <len (available_proxies ):
                    proxy_index =available_proxies [self .next_proxy_index %len (available_proxies )]
                    self .next_proxy_index +=1

                    # Check if this proxy is already assigned to another thread
                    if proxy_index not in self .thread_proxy_assignments .values ():
                        self .thread_proxy_assignments [thread_id ]=proxy_index
                        proxy =self .proxies [proxy_index ]
                        self .logger .info (f"Assigned proxy {proxy['host']}:{proxy['port']} to thread {thread_id}")
                        return proxy
                    attempts +=1

                # If all proxies are assigned, allow sharing (fallback)
                proxy_index =available_proxies [self .next_proxy_index %len (available_proxies )]
                self .next_proxy_index +=1
                self .thread_proxy_assignments [thread_id ]=proxy_index
                proxy =self .proxies [proxy_index ]
                self .logger .info (f"Assigned shared proxy {proxy['host']}:{proxy['port']} to thread {thread_id}")
                return proxy

            return None

    def _get_available_proxies (self ):
        """
        Get list of proxy indices that are not blacklisted.

        Returns:
            list: List of available proxy indices
        """
        available =[]
        for i ,proxy in enumerate (self .proxies ):
            proxy_key =self ._get_proxy_key (proxy )
            if proxy_key not in self .proxy_blacklist :
                available .append (i )
        return available

    def _cleanup_expired_blacklist (self ):
        """
        Remove proxies from blacklist that have exceeded the blacklist duration.
        """
        current_time =time .time ()
        expired_proxies =set ()

        for proxy_key in self .proxy_blacklist :
            if proxy_key in self .proxy_last_failure_time :
                time_since_failure =current_time -self .proxy_last_failure_time [proxy_key ]
                if time_since_failure >self .blacklist_duration :
                    expired_proxies .add (proxy_key )

        for proxy_key in expired_proxies :
            self .proxy_blacklist .discard (proxy_key )
            self .proxy_failure_counts [proxy_key ]=0  # Reset failure count
            self .logger .info (f"Removed {proxy_key} from blacklist after {self.blacklist_duration}s")

    def rotate_proxy (self ,thread_id =None ):
        """
        Rotate to the next proxy for the calling thread.
        In the new thread-safe system, this reassigns a new proxy to the thread.

        Args:
            thread_id: Optional thread identifier (uses current thread if None)

        Returns:
            dict: New proxy configuration or None if no proxies available
        """
        if thread_id is None :
            thread_id =threading .current_thread ().ident

        # Remove current thread assignment to force reassignment
        with self .proxy_assignment_lock :
            if thread_id in self .thread_proxy_assignments :
                old_proxy_index =self .thread_proxy_assignments [thread_id ]
                old_proxy =self .proxies [old_proxy_index ]
                del self .thread_proxy_assignments [thread_id ]
                self .logger .info (f"Thread {thread_id} rotating away from proxy {old_proxy['host']}:{old_proxy['port']}")

        # Get a new proxy assignment (this will acquire the lock again)
        new_proxy =self .get_proxy_for_thread (thread_id )
        if new_proxy :
            self .logger .info (f"Thread {thread_id} rotated to proxy {new_proxy['host']}:{new_proxy['port']}")
        return new_proxy

    def should_rotate_proxy (self ):
        """
        Check if it's time to rotate to the next proxy.

        Returns:
            bool: True if proxy should be rotated
        """
        with self .proxy_lock :
            return self .emails_sent_with_current_proxy >=self .emails_per_proxy 

    def increment_email_count (self ):
        """
        Increment the count of emails sent with the current proxy.
        """
        with self .proxy_lock :
            self .emails_sent_with_current_proxy +=1

    def blacklist_proxy (self ,proxy_config ,reason ="Connection failed"):
        """
        Add a proxy to the blacklist due to repeated failures.

        Args:
            proxy_config (dict): Proxy configuration to blacklist
            reason (str): Reason for blacklisting
        """
        proxy_key =self ._get_proxy_key (proxy_config )

        with self .proxy_assignment_lock :
            # Increment failure count
            self .proxy_failure_counts [proxy_key ]=self .proxy_failure_counts .get (proxy_key ,0 )+1
            self .proxy_last_failure_time [proxy_key ]=time .time ()

            # Blacklist if failure count exceeds threshold
            failure_threshold =3  # Blacklist after 3 consecutive failures
            if self .proxy_failure_counts [proxy_key ]>=failure_threshold :
                self .proxy_blacklist .add (proxy_key )
                self .logger .warning (f"Blacklisted proxy {proxy_key} after {self.proxy_failure_counts[proxy_key]} failures: {reason}")

                # Remove thread assignments for this proxy
                threads_to_reassign =[]
                for thread_id ,assigned_index in self .thread_proxy_assignments .items ():
                    if assigned_index <len (self .proxies ):
                        assigned_proxy =self .proxies [assigned_index ]
                        if self ._get_proxy_key (assigned_proxy )==proxy_key :
                            threads_to_reassign .append (thread_id )

                for thread_id in threads_to_reassign :
                    del self .thread_proxy_assignments [thread_id ]
                    self .logger .info (f"Removed blacklisted proxy assignment for thread {thread_id}")

    def record_proxy_success (self ,proxy_config ):
        """
        Record a successful connection for a proxy, resetting its failure count.

        Args:
            proxy_config (dict): Proxy configuration that succeeded
        """
        proxy_key =self ._get_proxy_key (proxy_config )

        with self .proxy_assignment_lock :
            # Reset failure count on success
            if proxy_key in self .proxy_failure_counts :
                self .proxy_failure_counts [proxy_key ]=0
                self .logger .debug (f"Reset failure count for proxy {proxy_key}")

    def is_proxy_blacklisted (self ,proxy_config ):
        """
        Check if a proxy is currently blacklisted.

        Args:
            proxy_config (dict): Proxy configuration to check

        Returns:
            bool: True if proxy is blacklisted
        """
        proxy_key =self ._get_proxy_key (proxy_config )
        return proxy_key in self .proxy_blacklist

    def _get_proxy_key (self ,proxy_config ):
        """
        Generate a unique key for a proxy configuration.

        Args:
            proxy_config (dict): Proxy configuration

        Returns:
            str: Unique proxy key
        """
        return f"{proxy_config['host']}:{proxy_config['port']}"

    def _get_connection_semaphore (self ,proxy_config ):
        """
        Get or create a semaphore for the given proxy to limit concurrent connections.

        Args:
            proxy_config (dict): Proxy configuration

        Returns:
            threading.Semaphore: Semaphore for the proxy
        """
        proxy_key =self ._get_proxy_key (proxy_config )

        if proxy_key not in self .connection_semaphores :
            self .connection_semaphores [proxy_key ]=threading .Semaphore (self .max_concurrent_connections )
            self .logger .debug (f"Created connection semaphore for proxy {proxy_key} with limit {self.max_concurrent_connections}")

        return self .connection_semaphores [proxy_key ]

    def _apply_connection_delay (self ,proxy_config ,min_delay =0.5 ):
        """
        Apply an adaptive delay between connections to the same proxy.
        Increases delay for proxies with recent failures.

        Args:
            proxy_config (dict): Proxy configuration
            min_delay (float): Minimum delay in seconds between connections
        """
        proxy_key =self ._get_proxy_key (proxy_config )
        current_time =time .time ()

        # Calculate adaptive delay based on failure count
        failure_count =self .proxy_failure_counts .get (proxy_key ,0 )
        adaptive_delay =min_delay

        if failure_count >0 :
            # Exponential backoff: 0.5s, 1s, 2s, 4s, max 8s
            adaptive_delay =min (min_delay *(2 **failure_count ),8.0 )
            self .logger .debug (f"Adaptive delay for proxy {proxy_key}: {adaptive_delay:.2f}s (failures: {failure_count})")

        if proxy_key in self .last_connection_time :
            time_since_last =current_time -self .last_connection_time [proxy_key ]
            if time_since_last <adaptive_delay :
                delay_needed =adaptive_delay -time_since_last
                self .logger .debug (f"Applying {delay_needed:.2f}s delay for proxy {proxy_key}")
                time .sleep (delay_needed )

        self .last_connection_time [proxy_key ]=time .time ()

    def get_adaptive_timeout (self ,proxy_config ,base_timeout =10 ):
        """
        Calculate adaptive timeout based on proxy performance.
        Increases timeout for proxies with recent failures.

        Args:
            proxy_config (dict): Proxy configuration
            base_timeout (int): Base timeout in seconds

        Returns:
            int: Adaptive timeout value
        """
        proxy_key =self ._get_proxy_key (proxy_config )
        failure_count =self .proxy_failure_counts .get (proxy_key ,0 )

        if failure_count >0 :
            # Increase timeout for problematic proxies: 10s, 15s, 20s, max 30s
            adaptive_timeout =min (base_timeout +(failure_count *5 ),30 )
            self .logger .debug (f"Adaptive timeout for proxy {proxy_key}: {adaptive_timeout}s (failures: {failure_count})")
            return adaptive_timeout

        return base_timeout

    def get_proxy_statistics (self ):
        """
        Get comprehensive statistics about proxy usage and performance.

        Returns:
            dict: Proxy statistics including success rates, failure counts, etc.
        """
        stats ={
            'total_proxies':len (self .proxies ),
            'blacklisted_proxies':len (self .proxy_blacklist ),
            'active_threads':len (self .thread_proxy_assignments ),
            'proxy_details':[],
            'thread_assignments':{}
        }

        # Per-proxy statistics
        for i ,proxy in enumerate (self .proxies ):
            proxy_key =self ._get_proxy_key (proxy )
            failure_count =self .proxy_failure_counts .get (proxy_key ,0 )
            is_blacklisted =proxy_key in self .proxy_blacklist
            last_failure =self .proxy_last_failure_time .get (proxy_key )

            proxy_stats ={
                'index':i ,
                'host':proxy ['host'],
                'port':proxy ['port'],
                'failure_count':failure_count ,
                'is_blacklisted':is_blacklisted ,
                'last_failure_time':last_failure
            }
            stats ['proxy_details'].append (proxy_stats )

        # Thread assignments
        for thread_id ,proxy_index in self .thread_proxy_assignments .items ():
            if proxy_index <len (self .proxies ):
                proxy =self .proxies [proxy_index ]
                stats ['thread_assignments'][str (thread_id )]={
                    'proxy_host':proxy ['host'],
                    'proxy_port':proxy ['port'],
                    'proxy_index':proxy_index
                }

        return stats

    def log_proxy_statistics (self ):
        """
        Log current proxy statistics for monitoring.
        """
        stats =self .get_proxy_statistics ()

        self .logger .info (f"=== PROXY STATISTICS ===")
        self .logger .info (f"Total proxies: {stats['total_proxies']}")
        self .logger .info (f"Blacklisted proxies: {stats['blacklisted_proxies']}")
        self .logger .info (f"Active threads: {stats['active_threads']}")

        if stats ['blacklisted_proxies']>0 :
            blacklisted =[]
            for proxy_detail in stats ['proxy_details']:
                if proxy_detail ['is_blacklisted']:
                    blacklisted .append (f"{proxy_detail['host']}:{proxy_detail['port']} (failures: {proxy_detail['failure_count']})")
            self .logger .warning (f"Blacklisted proxies: {', '.join(blacklisted)}")

        # Log thread assignments
        if stats ['thread_assignments']:
            self .logger .info ("Thread proxy assignments:")
            for thread_id ,assignment in stats ['thread_assignments'].items ():
                self .logger .info (f"  Thread {thread_id}: {assignment['proxy_host']}:{assignment['proxy_port']}")

    def get_smtp_connection_manager (self ,smtp_host ,smtp_port ,proxy_config =None ):
        """
        Get a context manager for SMTP connections with proper connection limiting.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            proxy_config (dict): Proxy configuration (uses current proxy if None)

        Returns:
            SMTPConnectionManager: Context manager for SMTP connections
        """
        return SMTPConnectionManager (self ,smtp_host ,smtp_port ,proxy_config )

    def configure_smtp_with_proxy (self ,smtp_host ,smtp_port ,proxy_config =None ):
        """
        Configure SMTP connection to use a SOCKS proxy.
        Supports both individual proxy connections and global monkey-patching.
        Note: This method does not use connection limiting. Use get_smtp_connection_manager for that.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            proxy_config (dict): Proxy configuration (uses current proxy if None)

        Returns:
            smtplib.SMTP: Configured SMTP object or None if failed
        """

        if self .use_global_proxy and self .global_proxy_active :
            return self ._create_global_proxy_smtp_connection (smtp_host ,smtp_port ,timeout =15 )

        if proxy_config is None :
            proxy_config =self .get_current_proxy ()

        if not proxy_config :
            self .logger .error ("No proxy configured - direct SMTP connections disabled for security")
            return None

        try :
            if proxy_config ['protocol']in ['socks4','socks5']:
                if socks is None :
                    self .logger .error ("PySocks library not available for SOCKS proxy support")
                    return None

                # Use adaptive timeout based on proxy performance
                adaptive_timeout =self .get_adaptive_timeout (proxy_config ,base_timeout =10 )
                return self ._create_socks_smtp_connection (
                smtp_host ,smtp_port ,proxy_config ,timeout =adaptive_timeout
                )

            elif proxy_config ['protocol']in ['http','https']:
                self .logger .warning ("HTTP proxy support for SMTP is not implemented")
                return None

        except Exception as e :
            self .logger .error (f"Failed to configure SMTP with proxy {proxy_config['host']}:{proxy_config['port']}: {str(e)}")
            return None

    def enable_global_proxy (self ,proxy_config =None ):
        """
        Enable global socket monkey-patching for all network connections.
        This affects ALL socket connections in the application.

        Args:
            proxy_config (dict): Proxy configuration to use globally
        """
        if not socks :
            self .logger .error ("PySocks library not available for global proxy")
            return False 

        if proxy_config is None :
            proxy_config =self .get_current_proxy ()

        if not proxy_config :
            self .logger .error ("No proxy configuration available for global proxy")
            return False 

        try :

            if not self .original_socket :
                self .original_socket =socket .socket 


            if proxy_config ['protocol']=='socks4':
                proxy_type =socks .SOCKS4 
            else :
                proxy_type =socks .SOCKS5 

            self .logger .info (f"Enabling global {proxy_config['protocol'].upper()} proxy: {proxy_config['host']}:{proxy_config['port']}")


            socks .set_default_proxy (
            proxy_type ,
            proxy_config ['host'],
            proxy_config ['port'],
            username =proxy_config .get ('username'),
            password =proxy_config .get ('password')
            )


            socket .socket =socks .socksocket 
            self .global_proxy_active =True 

            self .logger .info ("Global proxy enabled successfully")
            return True 

        except Exception as e :
            self .logger .error (f"Failed to enable global proxy: {str(e)}")
            return False 

    def disable_global_proxy (self ):
        """
        Disable global socket monkey-patching and restore original socket.
        """
        try :
            if self .original_socket and self .global_proxy_active :

                socket .socket =self .original_socket 
                self .global_proxy_active =False 
                self .logger .info ("Global proxy disabled successfully")
                return True 
            else :
                self .logger .warning ("Global proxy was not active or original socket not stored")
                return False 
        except Exception as e :
            self .logger .error (f"Failed to disable global proxy: {str(e)}")
            return False 

    def is_global_proxy_active (self ):
        """
        Check if global proxy is currently active.

        Returns:
            bool: True if global proxy is active, False otherwise
        """
        return self .global_proxy_active 

    def rotate_global_proxy (self ):
        """
        Rotate to the next proxy and update global proxy settings.
        """
        if not self .global_proxy_active :
            return False 


        self .rotate_proxy ()


        return self .enable_global_proxy ()

    def _create_global_proxy_smtp_connection (self ,smtp_host ,smtp_port ,timeout =15 ):
        """
        Create SMTP connection when global proxy is active.
        Since socket is globally monkey-patched, we can use standard SMTP.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            timeout (int): Connection timeout in seconds

        Returns:
            smtplib.SMTP: Standard SMTP connection (automatically uses global proxy)
        """
        try :
            self .logger .debug (f"Creating SMTP connection via global proxy to {smtp_host}:{smtp_port}")


            smtp =smtplib .SMTP (smtp_host ,smtp_port ,timeout =timeout )

            self .logger .debug (f"Global proxy SMTP connection established to {smtp_host}:{smtp_port}")
            return smtp 

        except Exception as e :
            self .logger .error (f"Global proxy SMTP connection failed: {str(e)}")
            return None 

    def _create_socks_smtp_connection (self ,smtp_host ,smtp_port ,proxy_config ,timeout =10 ):
        """
        Create a reliable SOCKS proxy connection for SMTP using the SocksSMTP class.
        This uses PySocks' create_connection method which is more robust than manual socket handling.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            proxy_config (dict): SOCKS proxy configuration
            timeout (int): Connection timeout in seconds

        Returns:
            SocksSMTP: SMTP connection through SOCKS proxy or None if failed
        """
        try :

            if proxy_config ['protocol']=='socks4':
                proxy_type =socks .SOCKS4 
                self .logger .debug (f"Using SOCKS4 proxy: {proxy_config['host']}:{proxy_config['port']}")
            else :
                proxy_type =socks .SOCKS5 
                self .logger .debug (f"Using SOCKS5 proxy: {proxy_config['host']}:{proxy_config['port']} with auth")


            self .logger .debug (f"Connecting to {smtp_host}:{smtp_port} via SOCKS proxy...")

            smtp =SocksSMTP (
            host =smtp_host ,
            port =smtp_port ,
            timeout =timeout ,
            proxy_type =proxy_type ,
            proxy_addr =proxy_config ['host'],
            proxy_port =proxy_config ['port'],
            proxy_username =proxy_config .get ('username'),
            proxy_password =proxy_config .get ('password'),
            proxy_rdns =True 
            )

            self .logger .debug (f"SocksSMTP connection established to {smtp_host}:{smtp_port}")
            # Record successful connection
            self .record_proxy_success (proxy_config )
            return smtp

        except (socket .timeout ,socks .ProxyConnectionError ,ConnectionRefusedError )as primary_error :
            self .logger .warning (f"SocksSMTP failed: {str(primary_error)}, trying alternative method...")

            # Check for TTL expired error and blacklist if needed
            if "0x06" in str (primary_error )or "TTL expired" in str (primary_error ):
                self .blacklist_proxy (proxy_config ,"TTL expired error")
            elif "timeout" in str (primary_error ).lower ():
                self .blacklist_proxy (proxy_config ,"Connection timeout")

            return self ._create_manual_socks_smtp_connection (
            smtp_host ,smtp_port ,proxy_config ,timeout
            )

        except socks .ProxyConnectionError as proxy_error :
            error_msg =str (proxy_error )
            self .logger .error (f"SOCKS proxy connection failed to {proxy_config['host']}:{proxy_config['port']}: {error_msg}")

            # Check for specific SOCKS5 errors that indicate proxy issues
            if "0x06" in error_msg or "TTL expired" in error_msg :
                self .blacklist_proxy (proxy_config ,"SOCKS5 TTL expired")
            elif "0x05" in error_msg or "Connection refused" in error_msg :
                self .blacklist_proxy (proxy_config ,"SOCKS5 connection refused")
            else :
                self .blacklist_proxy (proxy_config ,f"SOCKS5 error: {error_msg}")
            return None

        except socket .timeout :
            self .logger .error (f"SOCKS connection timeout after {timeout} seconds to {proxy_config['host']}:{proxy_config['port']}")
            self .blacklist_proxy (proxy_config ,"Socket timeout")
            return None

        except socket .gaierror as dns_error :
            self .logger .error (f"DNS resolution failed for SOCKS proxy {proxy_config['host']}: {str(dns_error)}")
            self .blacklist_proxy (proxy_config ,"DNS resolution failed")
            return None

        except Exception as e :
            error_msg =str (e )
            self .logger .error (f"SocksSMTP connection failed to {proxy_config['host']}:{proxy_config['port']}: {error_msg}")

            # Check for TTL expired in generic exceptions
            if "0x06" in error_msg or "TTL expired" in error_msg :
                self .blacklist_proxy (proxy_config ,"TTL expired in exception")
            else :
                self .blacklist_proxy (proxy_config ,f"Connection error: {error_msg}")
            return None

    def _create_manual_socks_smtp_connection (self ,smtp_host ,smtp_port ,proxy_config ,timeout =15 ):
        """
        Alternative SOCKS SMTP connection method with manual socket control.
        This method provides more granular control over the connection process.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            proxy_config (dict): SOCKS proxy configuration
            timeout (int): Connection timeout in seconds

        Returns:
            smtplib.SMTP: SMTP connection through SOCKS proxy or None if failed
        """
        import socket 
        sock =None 
        try :

            sock =socks .socksocket (socket .AF_INET ,socket .SOCK_STREAM )


            sock .settimeout (timeout )


            sock .setsockopt (socket .SOL_SOCKET ,socket .SO_KEEPALIVE ,1 )
            sock .setsockopt (socket .IPPROTO_TCP ,socket .TCP_NODELAY ,1 )


            if proxy_config ['protocol']=='socks4':
                proxy_type =socks .SOCKS4 
                sock .set_proxy (
                proxy_type ,
                proxy_config ['host'],
                proxy_config ['port'],
                username =proxy_config .get ('username')
                )
            else :
                proxy_type =socks .SOCKS5 
                sock .set_proxy (
                proxy_type ,
                proxy_config ['host'],
                proxy_config ['port'],
                username =proxy_config .get ('username'),
                password =proxy_config .get ('password')
                )

            self .logger .debug (f"Manual SOCKS: Connecting to {smtp_host}:{smtp_port}")


            max_retries =2 
            for attempt in range (max_retries ):
                try :
                    sock .connect ((smtp_host ,smtp_port ))
                    break 
                except (socket .timeout ,socks .ProxyConnectionError )as e :
                    if attempt ==max_retries -1 :
                        raise 
                    self .logger .debug (f"Connection attempt {attempt + 1} failed, retrying...")
                    time .sleep (1 )


            smtp =smtplib .SMTP ()
            smtp .sock =sock 
            smtp .file =sock .makefile ('rb')
            smtp .timeout =timeout 


            greeting_attempts =3 
            for attempt in range (greeting_attempts ):
                try :

                    sock .settimeout (8 )
                    code ,msg =smtp .getreply ()

                    if code ==220 :
                        self .logger .debug (f"Manual SOCKS: SMTP greeting OK: {code}")

                        sock .settimeout (timeout )
                        return smtp 
                    else :
                        self .logger .error (f"Manual SOCKS: Invalid SMTP greeting: {code} {msg}")
                        break 

                except socket .timeout :
                    if attempt ==greeting_attempts -1 :
                        self .logger .error ("Manual SOCKS: SMTP greeting timeout after retries")
                        break 
                    self .logger .debug (f"Greeting attempt {attempt + 1} timed out, retrying...")
                    time .sleep (0.5 )
                except Exception as e :
                    self .logger .error (f"Manual SOCKS: SMTP greeting error: {str(e)}")
                    break 


            smtp .close ()
            return None 

        except Exception as e :
            self .logger .error (f"Manual SOCKS connection failed: {str(e)}")
            if sock :
                try :
                    sock .close ()
                except :
                    pass 
            return None 

    def test_proxy_connection (self ,proxy_config ,timeout =3 ):
        """
        Test if a proxy is working by making a simple connection.

        Args:
            proxy_config (dict): Proxy configuration to test
            timeout (int): Connection timeout in seconds (default: 3)

        Returns:
            bool: True if proxy is working, False otherwise
        """
        try :
            if proxy_config ['protocol']in ['socks4','socks5']:
                if socks is None :
                    return False 

                sock =socks .socksocket ()

                if proxy_config ['protocol']=='socks4':
                    proxy_type =socks .SOCKS4 
                else :
                    proxy_type =socks .SOCKS5 

                sock .set_proxy (
                proxy_type ,
                proxy_config ['host'],
                proxy_config ['port'],
                username =proxy_config ['username'],
                password =proxy_config ['password']
                )


                sock .settimeout (timeout )
                sock .connect (('*******',53 ))
                sock .close ()
                return True 

            elif proxy_config ['protocol']in ['http','https']:

                proxy_url =f"{proxy_config['protocol']}://"
                if proxy_config ['username']and proxy_config ['password']:
                    proxy_url +=f"{proxy_config['username']}:{proxy_config['password']}@"
                proxy_url +=f"{proxy_config['host']}:{proxy_config['port']}"

                proxy_handler =urllib .request .ProxyHandler ({
                'http':proxy_url ,
                'https':proxy_url 
                })
                opener =urllib .request .build_opener (proxy_handler )


                test_urls =['http://www.google.com','http://www.example.com','http://httpbin.org/ip']
                for test_url in test_urls :
                    try :
                        request =urllib .request .Request (test_url )
                        response =opener .open (request ,timeout =timeout )
                        if response .status ==200 :
                            return True 
                    except :
                        continue 
                return False 

        except Exception as e :
            self .logger .debug (f"Proxy test failed for {proxy_config['host']}:{proxy_config['port']}: {str(e)}")
            return False 

    def get_working_proxies (self ):
        """
        Test all configured proxies and return a list of working ones.

        Returns:
            list: List of working proxy configurations
        """
        working_proxies =[]
        for proxy in self .proxies :
            if self .test_proxy_connection (proxy ):
                working_proxies .append (proxy )
                self .logger .info (f"Proxy {proxy['host']}:{proxy['port']} is working")
            else :
                self .logger .warning (f"Proxy {proxy['host']}:{proxy['port']} is not working")

        return working_proxies 

    def test_proxies_parallel (self ,proxies_to_test ,max_workers =10 ,timeout =3 ):
        """
        Test multiple proxies in parallel for faster results.

        Args:
            proxies_to_test (list): List of proxy configurations to test
            max_workers (int): Maximum number of concurrent tests
            timeout (int): Timeout for each proxy test

        Returns:
            list: List of tuples (proxy_config, is_working)
        """
        import concurrent .futures 

        def test_single_proxy (proxy_config ):
            try :
                is_working =self .test_proxy_connection (proxy_config ,timeout )
                return (proxy_config ,is_working )
            except Exception as e :
                self .logger .debug (f"Error testing proxy {proxy_config['host']}:{proxy_config['port']}: {str(e)}")
                return (proxy_config ,False )

        results =[]
        with concurrent .futures .ThreadPoolExecutor (max_workers =max_workers )as executor :
            future_to_proxy ={executor .submit (test_single_proxy ,proxy ):proxy for proxy in proxies_to_test }

            for future in concurrent .futures .as_completed (future_to_proxy ):
                try :
                    result =future .result ()
                    results .append (result )
                except Exception as e :
                    proxy =future_to_proxy [future ]
                    self .logger .debug (f"Exception testing proxy {proxy['host']}:{proxy['port']}: {str(e)}")
                    results .append ((proxy ,False ))

        return results 

    def get_current_proxy (self ):
        """
        Get the currently active proxy configuration.

        Returns:
            dict: Current proxy configuration or None if no proxy is active
        """
        if self .proxies and 0 <=self .current_proxy_index <len (self .proxies ):
            return self .proxies [self .current_proxy_index ]
        return None 


class MultiThreadedEmailSender :
    """
    A multi-threaded email sender that works with proxy rotation.
    """

    def __init__ (self ,num_threads =3 ,proxy_manager =None ):
        """
        Initialize the multi-threaded email sender.

        Args:
            num_threads (int): Number of worker threads to use
            proxy_manager (ProxyManager): Proxy manager instance
        """
        self .num_threads =num_threads 
        self .proxy_manager =proxy_manager 
        self .email_queue =queue .Queue ()
        self .result_queue =queue .Queue ()
        self .threads =[]
        self .stop_event =threading .Event ()
        self .logger =logging .getLogger ("MultiThreadedEmailSender")


        self .emails_sent =0 
        self .emails_failed =0 
        self .stats_lock =threading .Lock ()

    def add_email_to_queue (self ,email_data ):
        """
        Add an email to the sending queue.

        Args:
            email_data (dict): Email data containing recipient, sender info, etc.
        """
        self .email_queue .put (email_data )

    def worker_thread (self ,thread_id ):
        """
        Worker thread function for sending emails.

        Args:
            thread_id (int): Unique identifier for this thread
        """
        self .logger .info (f"Worker thread {thread_id} started")

        # Log initial proxy assignment
        if self .proxy_manager :
            initial_proxy =self .proxy_manager .get_proxy_for_thread ()
            if initial_proxy :
                self .logger .info (f"Thread {thread_id} assigned initial proxy: {initial_proxy['host']}:{initial_proxy['port']}")

        while not self .stop_event .is_set ():
            try :

                email_data =self .email_queue .get (timeout =0.5 )


                success =self .send_single_email (email_data ,thread_id )


                with self .stats_lock :
                    if success :
                        self .emails_sent +=1 
                    else :
                        self .emails_failed +=1 


                self .result_queue .put ({
                'thread_id':thread_id ,
                'email_data':email_data ,
                'success':success ,
                'timestamp':time .time ()
                })


                self .email_queue .task_done ()

            except queue .Empty :

                continue 
            except Exception as e :
                self .logger .error (f"Worker thread {thread_id} error: {str(e)}")

        self .logger .info (f"Worker thread {thread_id} stopped")

    def send_single_email (self ,email_data ,thread_id ):
        """
        Send a single email using the configured proxy with connection management.

        Args:
            email_data (dict): Email data
            thread_id (int): Thread identifier

        Returns:
            bool: True if email was sent successfully
        """
        try :
            # Check if proxy rotation is needed
            if self .proxy_manager and self .proxy_manager .should_rotate_proxy ():
                self .proxy_manager .rotate_proxy ()

            smtp_host =email_data .get ('smtp_host','smtp.gmail.com')
            smtp_port =email_data .get ('smtp_port',587 )

            # Use connection manager for proper connection limiting
            if self .proxy_manager :
                connection_manager =self .proxy_manager .get_smtp_connection_manager (smtp_host ,smtp_port )
            else :
                # For direct connections without proxy, create a simple context manager
                connection_manager =DirectSMTPConnectionManager (smtp_host ,smtp_port )

            with connection_manager as smtp :
                if not smtp :
                    self .logger .error (f"Thread {thread_id}: Failed to create SMTP connection")
                    return False

                # Configure SMTP connection
                smtp .starttls ()
                smtp .login (email_data ['sender_email'],email_data ['sender_password'])

                # Create email message
                msg =MIMEMultipart ()
                msg ['From']=formataddr ((email_data .get ('from_name',''),email_data ['sender_email']))
                msg ['To']=email_data ['recipient']
                msg ['Subject']=Header (email_data ['subject'],'utf-8')

                # Add content
                if email_data .get ('html_content'):
                    msg .attach (MIMEText (email_data ['html_content'],'html','utf-8'))
                else :
                    msg .attach (MIMEText (email_data .get ('text_content',''),'plain','utf-8'))

                # Send email
                smtp .sendmail (email_data ['sender_email'],email_data ['recipient'],msg .as_string ())

                # Increment email count for proxy rotation
                if self .proxy_manager :
                    self .proxy_manager .increment_email_count ()

                self .logger .info (f"Thread {thread_id}: Email sent to {email_data['recipient']} using {email_data['sender_email']}")
                return True

        except Exception as e :
            error_msg =str (e )
            self .logger .error (f"Thread {thread_id}: Failed to send email to {email_data['recipient']}: {error_msg}")

            # Handle specific "too many connections" error
            if any (keyword in error_msg .lower ()for keyword in ['too many connections','service unavailable','service not available']):
                self .logger .warning (f"Thread {thread_id}: GMX server connection limit reached - {error_msg}")
                # Don't save as failed sender for connection limit errors, as it's temporary
                # Add a longer delay before next attempt
                time .sleep (2.0 )
                return False
            elif any (keyword in error_msg .lower ()for keyword in ['authentication','login','password','username']):
                self ._save_failed_sender (email_data ['sender_email'],f"SMTP Auth Failed: {error_msg}")
            elif any (keyword in error_msg .lower ()for keyword in ['proxy','connection','timeout','ttl expired','0x06']):
                self .logger .warning (f"Thread {thread_id}: Proxy/connection error - {error_msg}")

                # For TTL expired errors, immediately try a different proxy
                if self .proxy_manager :
                    if "ttl expired" in error_msg .lower ()or "0x06" in error_msg :
                        self .logger .warning (f"Thread {thread_id}: TTL expired error detected, rotating proxy immediately")
                        # Try up to 2 more proxies for TTL expired errors
                        for retry_attempt in range (2 ):
                            new_proxy =self .proxy_manager .rotate_proxy ()
                            if new_proxy :
                                self .logger .info (f"Thread {thread_id}: Retry {retry_attempt + 1} with proxy {new_proxy['host']}:{new_proxy['port']}")
                                # Try sending with new proxy
                                retry_success =self ._retry_send_with_new_proxy (email_data ,thread_id )
                                if retry_success :
                                    return True
                                time .sleep (1.0 )  # Brief delay between retries
                            else :
                                break
                    else :
                        # For other proxy errors, just rotate once
                        self .proxy_manager .rotate_proxy ()
                        time .sleep (1.0 )

                # Don't save as failed sender for proxy errors that might be temporary
                if "ttl expired" not in error_msg .lower ()and "0x06" not in error_msg :
                    self ._save_failed_sender (email_data ['sender_email'],f"Proxy/Connection Error: {error_msg}")
            else :
                self ._save_failed_sender (email_data ['sender_email'],f"Send Failed: {error_msg}")

            return False

    def _retry_send_with_new_proxy (self ,email_data ,thread_id ):
        """
        Retry sending email with a new proxy after TTL expired error.

        Args:
            email_data (dict): Email data to send
            thread_id (int): Thread identifier

        Returns:
            bool: True if retry was successful
        """
        try :
            smtp_host =email_data .get ('smtp_host','smtp.gmail.com')
            smtp_port =email_data .get ('smtp_port',587 )

            # Use connection manager for proper connection limiting
            if self .proxy_manager :
                connection_manager =self .proxy_manager .get_smtp_connection_manager (smtp_host ,smtp_port )
            else :
                return False  # No proxy manager, can't retry

            with connection_manager as smtp :
                if not smtp :
                    self .logger .warning (f"Thread {thread_id}: Failed to create SMTP connection for retry")
                    return False

                # Configure SMTP connection
                smtp .starttls ()
                smtp .login (email_data ['sender_email'],email_data ['sender_password'])

                # Create email message
                msg =MIMEMultipart ()
                msg ['From']=formataddr ((email_data .get ('from_name',''),email_data ['sender_email']))
                msg ['To']=email_data ['recipient']
                msg ['Subject']=Header (email_data ['subject'],'utf-8')

                # Add content
                if email_data .get ('html_content'):
                    msg .attach (MIMEText (email_data ['html_content'],'html','utf-8'))
                else :
                    msg .attach (MIMEText (email_data .get ('text_content',''),'plain','utf-8'))

                # Send email
                smtp .sendmail (email_data ['sender_email'],email_data ['recipient'],msg .as_string ())

                # Increment email count for proxy rotation
                if self .proxy_manager :
                    self .proxy_manager .increment_email_count ()

                self .logger .info (f"Thread {thread_id}: Email retry successful to {email_data['recipient']} using {email_data['sender_email']}")
                return True

        except Exception as retry_e :
            self .logger .warning (f"Thread {thread_id}: Retry failed: {str(retry_e)}")
            return False

    def _save_failed_sender (self ,sender ,error_message ):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime 
        import os 

        failed_senders_file =f"{home}/failed_senders.txt"
        timestamp =datetime .datetime .now ().strftime ("%Y-%m-%d %H:%M:%S")


        entry =f"[{timestamp}] {sender} - {error_message}\n"


        entry_exists =False 
        if os .path .exists (failed_senders_file ):
            try :
                with open (failed_senders_file ,'r',encoding ='utf-8')as f :
                    existing_content =f .read ()

                    if sender in existing_content :
                        entry_exists =True 
            except Exception :
                pass 


        if not entry_exists :
            try :
                with open (failed_senders_file ,'a',encoding ='utf-8')as f :
                    f .write (entry )
                self .logger .info (f"Saved failed sender: {sender}")
            except Exception as e :
                self .logger .error (f"Failed to save failed sender {sender}: {str(e)}")

    def start_sending (self ):
        """
        Start the worker threads for sending emails.
        """
        self .stop_event .clear ()
        self .threads =[]

        for i in range (self .num_threads ):
            thread =threading .Thread (target =self .worker_thread ,args =(i ,))
            thread .daemon =True 
            thread .start ()
            self .threads .append (thread )

        self .logger .info (f"Started {self.num_threads} worker threads")

        # Log initial proxy statistics
        if self .proxy_manager :
            self .proxy_manager .log_proxy_statistics ()

    def stop_sending (self ):
        """
        Stop all worker threads.
        """
        self .stop_event .set ()


        for thread in self .threads :
            thread .join (timeout =5 )

        self .logger .info ("All worker threads stopped")

    def get_statistics (self ):
        """
        Get current sending statistics including proxy information.

        Returns:
            dict: Statistics including emails sent, failed, proxy stats, etc.
        """
        with self .stats_lock :
            stats ={
                'emails_sent':self .emails_sent ,
                'emails_failed':self .emails_failed ,
                'queue_size':self .email_queue .qsize (),
                'threads_active':len ([t for t in self .threads if t .is_alive ()])
            }

            # Add proxy statistics if available
            if self .proxy_manager :
                proxy_stats =self .proxy_manager .get_proxy_statistics ()
                stats ['proxy_stats']=proxy_stats

            return stats


class HealthCheckWorker (QObject ):
    """Worker class for running health checks in a separate thread"""
    finished =pyqtSignal ()
    log_message =pyqtSignal (str )
    progress_update =pyqtSignal (str )

    def __init__ (self ,senders_file_name ,failed_senders_file ,use_proxy =False ,emails_per_proxy =5 ,use_multithreading =True ,use_global_proxy =False ):
        super ().__init__ ()
        self .senders_file_name =senders_file_name 
        self .failed_senders_file =failed_senders_file 
        self .cancel =False 
        self .use_proxy =use_proxy 
        self .emails_per_proxy =emails_per_proxy 
        self .use_multithreading =use_multithreading 
        self .use_global_proxy =use_global_proxy 
        self .proxy_manager =None 
        self .checks_since_proxy_change =0 


        if self .use_proxy :
            try :
                self .proxy_manager =ProxyManager (
                emails_per_proxy =self .emails_per_proxy ,
                use_global_proxy =self .use_global_proxy ,
                max_concurrent_connections = 4
                )

                if self .proxy_manager .load_proxies_from_file (f"{home}/proxies.txt"):
                    self .progress_update .emit (f" Proxy manager initialized with {len(self.proxy_manager.proxies)} proxies for health checks")


                    if self .use_global_proxy :
                        if self .proxy_manager .enable_global_proxy ():
                            self .progress_update .emit (" Global proxy mode enabled for health checks")
                        else :
                            self .progress_update .emit ("️ Failed to enable global proxy mode")
                else :
                    self .progress_update .emit ("️ No proxies loaded for health checks, using direct connection")
                    self .use_proxy =False 
            except Exception as e :
                self .progress_update .emit (f"️ Failed to initialize proxy manager: {str(e)}")
                self .use_proxy =False 

    def get_smtp_connection (self ,smtp_host ,port =587 ,timeout =15 ):
        """
        Get an SMTP connection, optionally through a proxy.

        Args:
            smtp_host (str): SMTP server hostname
            port (int): SMTP server port
            timeout (int): Connection timeout in seconds (reduced from 30 to 15)

        Returns:
            smtplib.SMTP: SMTP connection object or None if failed
        """
        import smtplib 
        import ssl 

        try :
            if self .use_proxy and self .proxy_manager :

                if self .checks_since_proxy_change >=self .emails_per_proxy :
                    self .proxy_manager .rotate_proxy ()
                    self .checks_since_proxy_change =0 
                    self .progress_update .emit (" Rotated to next proxy for health checks")


                self .progress_update .emit (f" Connecting to {smtp_host}:{port} via SOCKS5 proxy...")
                smtp_conn =self .proxy_manager .configure_smtp_with_proxy (smtp_host ,port )
                if smtp_conn :
                    self .checks_since_proxy_change +=1 
                    return smtp_conn 
                else :

                    self .progress_update .emit (" SOCKS5 proxy connection failed - ABORTING (direct connection disabled for security)")
                    return None 
            else :

                self .progress_update .emit (" No proxy configured - direct SMTP connections disabled for security")
                return None 

        except Exception as e :
            self .progress_update .emit (f" Failed to create SMTP connection to {smtp_host}:{port}: {str(e)}")
            return None 

    def run (self ):
        """Run the health check in a separate thread"""

        self .progress_update .emit ("Starting sender health check...\n")


        import sys 
        import os 
        import traceback 




        try :
            with open (self .senders_file_name ,'r')as senders_file :
                senders =senders_file .readlines ()


            gmail_senders =[]
            yahoo_senders =[]
            gmx_senders =[]
            other_senders =[]

            for sender in senders :
                sender =sender .strip ()
                if not sender :
                    continue 

                if "@gmail.com"in sender :
                    gmail_senders .append (sender )
                elif "@yahoo.com"in sender :
                    yahoo_senders .append (sender )
                elif "@gmx.com"in sender or "@gmx.de"in sender or "@gmx.us"in sender :
                    gmx_senders .append (sender )
                else :
                    other_senders .append (sender )


            total_senders =0 
            successful_senders =0 

            def test_single_sender (sender_info ,smtp_host ,group_name ):
                """Test a single sender - designed for multi-threading"""
                nonlocal total_senders ,successful_senders 

                if self .cancel :
                    return 

                try :

                    if ';'in sender_info :
                        sender ,passwd =sender_info .split (';')
                    elif ':'in sender_info :
                        sender ,passwd =sender_info .split (':')
                    else :
                        self .progress_update .emit (f" Error: Invalid format for {sender_info}")
                        return 


                    self .progress_update .emit (f" Testing {sender}...")


                    import ssl 
                    import socket 
                    import smtplib 


                    max_retries =1 
                    timeout_value =8 

                    for retry in range (max_retries +1 ):
                        if self .cancel :
                            return 

                        try :

                            smtp =self .get_smtp_connection (smtp_host ,port =587 ,timeout =timeout_value )
                            if smtp is None :
                                raise Exception ("Failed to establish SMTP connection")

                            with smtp :
                                context =ssl .create_default_context ()
                                smtp .starttls (context =context )
                                smtp .login (sender ,passwd )


                                proxy_info =""
                                if self .use_proxy and self .proxy_manager :
                                    current_proxy =self .proxy_manager .get_current_proxy ()
                                    if current_proxy :
                                        proxy_info =f" (via {current_proxy['host']}:{current_proxy['port']})"

                                self .progress_update .emit (f" {sender}: Login successful{proxy_info}")
                                successful_senders +=1 
                                break 

                        except (smtplib .SMTPServerDisconnected ,TimeoutError ,ConnectionRefusedError ,socket .timeout )as e :
                            if retry <max_retries :
                                retry_msg =f"️ {sender}: Retry {retry+1}/{max_retries} - {str(e)}"
                                if self .use_proxy and self .proxy_manager :
                                    retry_msg +=" (trying next proxy)"
                                    self .proxy_manager .rotate_proxy ()
                                self .progress_update .emit (retry_msg )
                                time .sleep (0.3 )
                            else :
                                raise 
                        except Exception as e :

                            if self .use_proxy and "proxy"in str (e ).lower ():
                                self .progress_update .emit (f" {sender}: Proxy error - ABORTING (direct connection disabled for security)")
                            raise 

                except Exception as e :
                    error_msg =f"Health check: {str(e)}"
                    self .progress_update .emit (f" {sender}: Login failed - {str(e)}")
                    self .save_failed_sender (sender ,error_msg )

            def test_sender_group (senders ,smtp_host ,group_name ):
                nonlocal total_senders ,successful_senders 

                if not senders :
                    self .progress_update .emit (f"No {group_name} senders found.")
                    return 

                total_senders +=len (senders )

                if self .use_multithreading :
                    self .progress_update .emit (f"\nTesting {len(senders)} {group_name} senders using multi-threading...")


                    from concurrent .futures import ThreadPoolExecutor ,as_completed 


                    max_workers =min (len (senders ),5 )
                    self .progress_update .emit (f" Starting {max_workers} worker threads for parallel testing...")

                    with ThreadPoolExecutor (max_workers =max_workers )as executor :

                        future_to_sender ={
                        executor .submit (test_single_sender ,sender_info ,smtp_host ,group_name ):sender_info 
                        for sender_info in senders 
                        }


                        for future in as_completed (future_to_sender ):
                            if self .cancel :
                                self .progress_update .emit ("Health check cancelled.")
                                return 

                            try :
                                future .result ()
                            except Exception as e :

                                pass 
                else :

                    self .progress_update .emit (f"\nTesting {len(senders)} {group_name} senders sequentially...")

                    for i ,sender_info in enumerate (senders ):
                        if self .cancel :
                            self .progress_update .emit ("Health check cancelled.")
                            return 


                        if i >0 :
                            time .sleep (0.2 )

                        test_single_sender (sender_info ,smtp_host ,group_name )




            test_sender_group (gmail_senders ,"smtp.gmail.com","Gmail")
            test_sender_group (gmx_senders ,"mail.gmx.com","GMX")
            test_sender_group (yahoo_senders ,"smtp.mail.yahoo.com","Yahoo")
            test_sender_group (other_senders ,"smtp.office365.com","Other")


            self .progress_update .emit (f"\n--- Health Check Complete ---")
            self .progress_update .emit (f"Total senders tested: {total_senders}")
            self .progress_update .emit (f"Successful logins: {successful_senders}")
            self .progress_update .emit (f"Failed logins: {total_senders - successful_senders}")

            if total_senders >0 :
                success_rate =(successful_senders /total_senders )*100 
                self .progress_update .emit (f"Success rate: {success_rate:.1f}%")


            if self .use_proxy and self .proxy_manager :
                current_proxy =self .proxy_manager .get_current_proxy ()
                if current_proxy :
                    self .progress_update .emit (f"Proxy usage: Enabled (current: {current_proxy['host']}:{current_proxy['port']})")
                    self .progress_update .emit (f"Checks performed with current proxy: {self.checks_since_proxy_change}")
                    self .progress_update .emit (f"Total proxies available: {len(self.proxy_manager.proxies)}")
                else :
                    self .progress_update .emit (f"Proxy usage: Enabled but no proxy available")
                    self .progress_update .emit (f"Total proxies loaded: {len(self.proxy_manager.proxies) if self.proxy_manager.proxies else 0}")
            else :
                self .progress_update .emit (f"Proxy usage: Disabled (direct connections)")

        except Exception as e :
            self .progress_update .emit (f"Error during health check: {str(e)}")




        self .finished .emit ()

    def save_failed_sender (self ,sender ,error_message ):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime 
        import os 

        timestamp =datetime .datetime .now ().strftime ("%Y-%m-%d %H:%M:%S")
        new_entry =f"{timestamp} | {sender} | {error_message}\n"

        try :

            if not os .path .exists (self .failed_senders_file ):

                with open (self .failed_senders_file ,'w',encoding ='utf-8')as file :
                    file .write ("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file .write (new_entry )
                return 


            existing_entries =[]
            duplicate_found =False 

            with open (self .failed_senders_file ,'r',encoding ='utf-8')as file :
                existing_entries =file .readlines ()

            for entry in existing_entries :
                if entry .startswith ('#')or not entry .strip ():
                    continue 


                parts =entry .split (' | ',2 )
                if len (parts )>=3 and parts [1 ]==sender and parts [2 ].strip ()==error_message :
                    duplicate_found =True 
                    break 


            if not duplicate_found :
                with open (self .failed_senders_file ,'a',encoding ='utf-8')as file :
                    file .write (new_entry )
        except Exception as e :
            self .progress_update .emit (f"Error saving failed sender: {str(e)}")

    def cancel_check (self ):
        """Cancel the health check"""
        self .cancel =True 


class Worker (QObject ):
    finished =pyqtSignal ()
    log_message =pyqtSignal (str )
    show_message_box_signal =pyqtSignal ()
    pass_result_to_worker_signal =pyqtSignal (bool )
    cancel_send_signal =pyqtSignal ()
    stop_signal =pyqtSignal ()
    all_emails_sent_signal =pyqtSignal ()



    def __init__ (self ,messageBox ,subject_value ,from_value ,limit_spin_value ,delay_value ,html_file_name ,groups_file ,test_mode_value ,isp_value ,senders_file_name ,rotation_checked ,use_proxy =False ,emails_per_proxy =5 ,num_threads =3 ,use_global_proxy =False ):
        super ().__init__ ()
        self .subject_value =subject_value 
        self .from_value =from_value 
        self .limit_spin_value =limit_spin_value 
        self .html_file_name =html_file_name 
        self .groups_file_name =groups_file 
        self .test_mode_value =test_mode_value 
        self .isp_value =isp_value 
        self .delay_spin =delay_value 
        self .senders_file_name =senders_file_name 
        self .rotation_checked =rotation_checked 
        self .failed_senders_file =f"{home}/failed_senders.txt"
        self .use_proxy =use_proxy 
        self .emails_per_proxy =emails_per_proxy 
        self .num_threads =num_threads 
        self .use_global_proxy =use_global_proxy 


        self .proxy_manager =None 
        if self .use_proxy :
            try :
                self .proxy_manager =ProxyManager (
                emails_per_proxy =self .emails_per_proxy ,
                use_global_proxy =self .use_global_proxy ,
                max_concurrent_connections =4
                )
                if self .proxy_manager .load_proxies_from_file (f"{home}/proxies.txt"):
                    self .log_message .emit (f"Proxy manager initialized with {len(self.proxy_manager.proxies)} proxies")


                    if use_global_proxy :
                        if self .proxy_manager .enable_global_proxy ():
                            self .log_message .emit (" Global proxy mode enabled for email sending")
                        else :
                            self .log_message .emit ("️ Failed to enable global proxy mode")
                else :
                    self .log_message .emit ("️ No proxies loaded, using direct connection")
                    self .use_proxy =False 
            except Exception as e :
                self .log_message .emit (f"️ Failed to initialize proxy manager: {str(e)}")
                self .use_proxy =False 


        self .email_sender =MultiThreadedEmailSender (
        num_threads =self .num_threads ,
        proxy_manager =self .proxy_manager if self .use_proxy else None 
        )
        self .cancel_send =False 
        self .messageBox =messageBox 
        self .pass_result_to_worker_signal .connect (self .handle_message_box_result )
        self .cancel_send_signal .connect (self .handle_cancel_request )
        self .stop_signal .connect (self .handle_cancel_request )
        logging .basicConfig (
        filename ='app.log',
        level =logging .DEBUG ,
        format ='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self .logger =logging .getLogger ("Worker")



    def handle_message_box_result (self ,result ):
        if result :
            return 
        else :
            self .cancel_send_signal .emit ()



    def handle_cancel_request (self ):
        self .cancel_send =True 

    def save_failed_sender (self ,sender ,error_message ):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime 
        import os 

        timestamp =datetime .datetime .now ().strftime ("%Y-%m-%d %H:%M:%S")
        new_entry =f"{timestamp} | {sender} | {error_message}\n"

        try :

            if not os .path .exists (self .failed_senders_file ):

                with open (self .failed_senders_file ,'w',encoding ='utf-8')as file :
                    file .write ("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file .write (new_entry )
                self .log_message .emit (f"Created new failed senders file: {self.failed_senders_file}")
                return 


            existing_entries =[]
            duplicate_found =False 

            with open (self .failed_senders_file ,'r',encoding ='utf-8')as file :
                existing_entries =file .readlines ()

            for entry in existing_entries :
                if entry .startswith ('#')or not entry .strip ():
                    continue 


                parts =entry .split (' | ',2 )
                if len (parts )>=3 and parts [1 ]==sender and parts [2 ].strip ()==error_message :
                    duplicate_found =True 
                    break 


            if not duplicate_found :
                with open (self .failed_senders_file ,'a',encoding ='utf-8')as file :
                    file .write (new_entry )
                self .log_message .emit (f"Saved failed sender to {self.failed_senders_file}")

        except Exception as e :
            self .log_message .emit (f"Error saving failed sender: {str(e)}")



    def run (self ):
        """Run the email sending process using multi-threaded approach with proxy support"""
        try :
            self .log_message .emit ("Starting email sending process...")


            if self .use_proxy and self .proxy_manager :
                self .log_message .emit ("Initializing proxy connections...")
                working_proxies =self .proxy_manager .get_working_proxies ()
                if working_proxies :
                    self .log_message .emit (f" Found {len(working_proxies)} working proxies")
                else :
                    self .log_message .emit ("️ No working proxies found, using direct connection")
                    self .use_proxy =False 
            else :
                self .log_message .emit (" Using direct connection (no proxy)")


            self .email_sender .start_sending ()
            self .log_message .emit (f"Started {self.num_threads} worker threads")


            self .log_message .emit (" About to call _load_and_process_emails()...")
            self ._load_and_process_emails ()
            self .log_message .emit (" _load_and_process_emails() completed")

        except Exception as e :
            self .log_message .emit (f" Error in email sending process: {str(e)}")
            import traceback 
            self .logger .error (f"Error in run method: {traceback.format_exc()}")
        finally :

            if hasattr (self ,'email_sender'):
                self .email_sender .stop_sending ()
            self .finished .emit ()

    def _load_and_process_emails (self ):
        """Load email addresses and senders, then process them"""
        try :
            self .log_message .emit (" Loading email addresses and senders...")

            gmail_isps =[]
            yahoo_isps =[]
            other_isps =[]
            gmx_isps =[]


            data_file =f"{home}/email-test.txt"if self .test_mode_value else f"{home}/{self.groups_file_name}"
            self .log_message .emit (f" Loading emails from: {data_file}")
            self .log_message .emit (f" Test mode: {self.test_mode_value}")

            try :
                with open (data_file ,'r')as f :
                    email_addresses =[addr .strip ()for addr in f .readlines ()if addr .strip ()]
                self .log_message .emit (f" Loaded {len(email_addresses)} email addresses")
                if email_addresses :
                    self .log_message .emit (f" First email: {email_addresses[0]}")
            except FileNotFoundError :
                self .log_message .emit (f" Email file not found: {data_file}")
                return 
            except Exception as e :
                self .log_message .emit (f" Error loading emails: {str(e)}")
                return 


            senders_file_path =f"{home}/{self.senders_file_name}"
            self .log_message .emit (f" Loading senders from: {senders_file_path}")

            try :
                with open (senders_file_path )as senders_file :
                    senders =senders_file .readlines ()
                self .log_message .emit (f" Loaded {len(senders)} senders")
                if senders :
                    self .log_message .emit (f" First sender: {senders[0].strip()}")
            except FileNotFoundError :
                self .log_message .emit (f" Senders file not found: {senders_file_path}")
                return 
            except Exception as e :
                self .log_message .emit (f" Error loading senders: {str(e)}")
                return 

            for sender in senders :
                sender =sender .strip ()
                if "@gmail.com"in sender :
                    gmail_isps .append (sender )
                elif "@yahoo.com"in sender :
                    yahoo_isps .append (sender )
                elif "@gmx.com"in sender :
                    gmx_isps .append (sender )
                else :
                    other_isps .append (sender )

            self .log_message .emit (f" Categorized senders: Gmail({len(gmail_isps)}), Yahoo({len(yahoo_isps)}), GMX({len(gmx_isps)}), Other({len(other_isps)})")
            self .log_message .emit (f" Selected ISP from UI: '{self.isp_value}'")
            self .log_message .emit (f" Sender rotation: {'Enabled' if self.rotation_checked else 'Disabled'}")
            if self .rotation_checked :
                self .log_message .emit (f" Emails per sender: {self.limit_spin_value}")


            if self .isp_value =="Gmail":
                senders_acc =gmail_isps 
                smtp_host ="smtp.gmail.com"
                smtp_port =587 
            elif self .isp_value =="Gmx":
                senders_acc =gmx_isps 
                smtp_host ="mail.gmx.com"
                smtp_port =587 
            elif self .isp_value =="Yahoo":
                senders_acc =yahoo_isps 
                smtp_host ="smtp.mail.yahoo.com"
                smtp_port =587 
            else :
                senders_acc =other_isps 
                smtp_host ="smtp.office365.com"
                smtp_port =587 

            self .log_message .emit (f" Selected ISP: {self.isp_value} with {len(senders_acc)} senders")

            if not senders_acc :
                self .log_message .emit (f" No senders found for ISP: {self.isp_value}")
                return 


            if not self .test_mode_value and self .rotation_checked :
                needed_senders_number =int (len (email_addresses )/self .limit_spin_value )
                if needed_senders_number >len (senders_acc ):
                    self .log_message .emit (f"️ Rotation enabled: Need {needed_senders_number} senders but only have {len(senders_acc)}")
                    self .log_message .emit (f" Consider disabling rotation or adding more {self.isp_value} senders")
                    self .show_message_box_signal .emit ()
                    return 
            elif not self .test_mode_value and not self .rotation_checked :
                self .log_message .emit (f" Rotation disabled: Using {len(senders_acc)} sender(s) for {len(email_addresses)} emails")

            if not senders_acc :
                self .log_message .emit (f" No {self.isp_value} senders found!")
                return 

            self .log_message .emit (f" Processing {len(email_addresses)} emails with {len(senders_acc)} senders")


            try :
                with open (f"{home}/{self.html_file_name}",'r',encoding ='utf-8')as html_file :
                    html_content =html_file .read ()
            except Exception as e :
                self .log_message .emit (f" Failed to load HTML content: {str(e)}")
                return 


            self ._create_and_queue_emails (email_addresses ,senders_acc ,smtp_host ,smtp_port ,html_content )


            self ._monitor_email_progress ()

        except Exception as e :
            self .log_message .emit (f" Critical error in _load_and_process_emails: {str(e)}")
            import traceback 
            self .logger .error (f"Critical error in _load_and_process_emails: {traceback.format_exc()}")

            if hasattr (self ,'email_sender'):
                self .email_sender .stop_sending ()

    def _create_and_queue_emails (self ,email_addresses ,senders_acc ,smtp_host ,smtp_port ,html_content ):
        """Create email jobs and add them to the sending queue"""
        self .log_message .emit (f" Creating email jobs for {len(email_addresses)} recipients...")

        sender_index =0 
        sender_count =0 
        emails_per_sender =self .limit_spin_value 
        emails_queued =0 

        for to_addr in email_addresses :
            if self .cancel_send :
                break 


            if self .rotation_checked and sender_count >=emails_per_sender :
                sender_index =(sender_index +1 )%len (senders_acc )
                sender_count =0 
                self .log_message .emit (f"Rotating to next sender (index {sender_index + 1}/{len(senders_acc)})")

            try :
                sender_info =senders_acc [sender_index ]

                if ';'in sender_info :
                    sender_email ,sender_password =sender_info .split (';',1 )
                elif ':'in sender_info :
                    sender_email ,sender_password =sender_info .split (':',1 )
                else :
                    raise ValueError ("Sender info doesn't contain separator")

                sender_email =sender_email .strip ()
                sender_password =sender_password .strip ()


                email_data ={
                'recipient':to_addr .strip (),
                'sender_email':sender_email ,
                'sender_password':sender_password ,
                'subject':self .subject_value ,
                'from_name':self .from_value ,
                'html_content':html_content ,
                'smtp_host':smtp_host ,
                'smtp_port':smtp_port 
                }


                self .email_sender .add_email_to_queue (email_data )
                sender_count +=1 
                emails_queued +=1 

            except Exception as e :
                self .log_message .emit (f" Error processing sender {sender_index}: {str(e)}")
                continue 

        self .log_message .emit (f" Successfully queued {emails_queued} emails for sending")


        queue_size =self .email_sender .email_queue .qsize ()
        self .log_message .emit (f" Email queue size: {queue_size}")

        if queue_size ==0 :
            self .log_message .emit ("️ WARNING: No emails were added to the queue!")

    def _monitor_email_progress (self ):
        """Monitor the progress of email sending"""

        time .sleep (0.5 )

        total_emails =self .email_sender .email_queue .qsize ()
        self .log_message .emit (f" Starting to send {total_emails} emails using {self.num_threads} threads")

        if total_emails ==0 :
            self .log_message .emit (" No emails in queue to send!")
            return 


        last_stats =None
        start_time =time .time ()
        last_proxy_stats_time =start_time
        while True :
            if self .cancel_send :
                self .email_sender .stop_sending ()
                self .log_message .emit (" Email sending cancelled by user")
                break 

            stats =self .email_sender .get_statistics ()


            if stats !=last_stats :
                emails_sent =stats ['emails_sent']
                emails_failed =stats ['emails_failed']
                queue_size =stats ['queue_size']
                threads_active =stats ['threads_active']

                if emails_sent +emails_failed >0 :
                    self .log_message .emit (f" Progress: {emails_sent} sent, {emails_failed} failed, {queue_size} remaining")

                    # Log proxy statistics if available
                    if 'proxy_stats' in stats and stats ['proxy_stats']:
                        proxy_stats =stats ['proxy_stats']
                        if proxy_stats ['blacklisted_proxies']>0 :
                            self .log_message .emit (f" Proxy status: {proxy_stats['blacklisted_proxies']}/{proxy_stats['total_proxies']} blacklisted")

                last_stats =stats


            current_time =time .time ()

            # Log detailed proxy statistics every 30 seconds
            if current_time -last_proxy_stats_time >30 :
                if self .proxy_manager :
                    self .proxy_manager .log_proxy_statistics ()
                last_proxy_stats_time =current_time

            if stats ['queue_size']==0 and stats ['threads_active']==0 :
                self .log_message .emit (" All emails processed successfully")
                break
            elif current_time -start_time >300 :
                self .log_message .emit ("️ Email sending timeout reached (5 minutes)")
                self .email_sender .stop_sending ()
                break


            try :
                while True :
                    result =self .email_sender .result_queue .get_nowait ()
                    if result ['success']:
                        self .log_message .emit (f" Email sent to {result['email_data']['recipient']} using {result['email_data']['sender_email']}")
                    else :
                        self .log_message .emit (f" Failed to send email to {result['email_data']['recipient']}")

                        self .save_failed_sender (result ['email_data']['sender_email'],"Send failed")
            except queue .Empty :
                pass 


            time .sleep (0.1 )


        final_stats =self .email_sender .get_statistics ()
        self .log_message .emit (f" Email sending completed: {final_stats['emails_sent']} sent, {final_stats['emails_failed']} failed")

        if final_stats ['emails_sent']>0 :
            self .all_emails_sent_signal .emit ()



class MainWindow (QMainWindow ):
    return_result_signal =pyqtSignal (int )

    def __init__ (self ):
        super ().__init__ ()
        uic .loadUi (f"{home}/smtp.ui",self )
        self .html_file_name ="offre.html"
        self .html_file_path =f"{home}/{self.html_file_name}"
        self .senders_file_name ="senders.txt"
        self .groups_file_name ="groups.txt"
        self .groups_test_file_name ="email-test.txt"
        self .saved_data_js =f"{home}/saved_data.json"
        self .failed_senders_file =f"{home}/failed_senders.txt"
        self .messageBox =QMessageBox ()
        logging .basicConfig (
        filename ='app.log',
        level =logging .DEBUG ,
        format ='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self .logger =logging .getLogger ("App")


        self .proxy_settings ={
        'use_proxy':False ,
        'emails_per_proxy':5 ,
        'num_threads':3 
        }


        self .check_dependencies ()

        self .setup_ui ()
        self .load_saved_data ()
        self .return_result_signal .connect (self .handle_returned_result )

    def check_dependencies (self ):
        """Check if all required dependencies are installed"""
        pass 

    def setup_ui (self ):
        old_style =self .rotation_check .styleSheet ()
        new_css =f"""
        QCheckBox::indicator:checked {{
            background-image: url("{home}/img/check.svg");
        }}
        """
        arrows_img =f"""
            QSpinBox::up-arrow, QSpinBox::down-arrow {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow {{
                image: url("{home}/img/down.svg");
            }}
            QSpinBox::up-arrow:hover {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow:hover {{
                image: url("{home}/img/down.svg");
            }}
        """
        self .limit_spin .setStyleSheet (self .limit_spin .styleSheet ()+arrows_img )
        self .delay_spin .setStyleSheet (self .delay_spin .styleSheet ()+arrows_img )
        new_check_img =old_style +new_css 
        self .test_mode .setStyleSheet (new_check_img )
        self .only_groups .setStyleSheet (new_check_img )
        self .rotation_check .setStyleSheet (new_check_img )
        edit_icon =QIcon (f"{home}/img/edit.svg")
        self .edit_groups .setIcon (edit_icon )
        self .edit_senders .setIcon (edit_icon )
        self .edit_test_emails .setIcon (edit_icon )
        self .edit_btn .clicked .connect (self .editBtn )
        self .preview_btn .clicked .connect (self .prevBtn )
        self .edit_groups .clicked .connect (partial (self .editfile ,"Groups"))
        self .edit_senders .clicked .connect (partial (self .editfile ,"Senders"))
        self .edit_test_emails .clicked .connect (partial (self .editfile ,"Test"))


        from PyQt6 .QtWidgets import QPushButton 

        button_style ="""
            QPushButton {
                padding: 5px 10px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f5f5f5;
                color: #000;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
                border: 1px solid #aaa;
            }
        """

        self .view_failed_btn =QPushButton ("View Failed Senders",self )
        self .view_failed_btn .setStyleSheet (button_style )
        self .view_failed_btn .clicked .connect (self .view_failed_senders )

        self .health_check_btn =QPushButton ("Health Check",self )
        self .health_check_btn .setStyleSheet (button_style )
        self .health_check_btn .clicked .connect (self .health_check_senders )

        self .proxy_config_btn =QPushButton ("Proxy Config",self )
        self .proxy_config_btn .setStyleSheet (button_style )
        self .proxy_config_btn .clicked .connect (self .configure_proxies )


        self .horizontalLayoutWidget_7 .layout ().addWidget (self .view_failed_btn )
        self .horizontalLayoutWidget_7 .layout ().addWidget (self .health_check_btn )
        self .horizontalLayoutWidget_7 .layout ().addWidget (self .proxy_config_btn )
        with open (f"{home}/{self.senders_file_name}",'r')as send_file :
            self .senders_number =len (send_file .readlines ())
        send_file .close ()
        self .senders_num .setText (str (self .senders_number ))
        with open (f"{home}/{self.groups_file_name}",'r')as groups_file :
            self .groups_number =len (groups_file .readlines ())
        groups_file .close ()
        self .groups_num .setText (str (self .groups_number ))
        with open (f"{home}/{self.groups_test_file_name}",'r')as test_groups_file :
            self .test_groups_number =len (test_groups_file .readlines ())
        test_groups_file .close ()
        try :
            self .send_btn .clicked .connect (self .send )
        except Exception as e :
            self .logger .error (f"Error while trying to send >> {str(e)}")
            QMessageBox .critical (self ,"Error","Please Check Logs")
        self .test_mode .stateChanged .connect (self .test_mode_check )
        self .log_text .setReadOnly (True )
        self .log_text .setVerticalScrollBarPolicy (Qt .ScrollBarPolicy .ScrollBarAlwaysOn )
        self .log_text .setPlainText (f"Starting Smtp..."+"\n")


    def getting_started (self ):
        if self .senders_number ==0 :
            QMessageBox .critical (self ,"Error Senders File Empty","Please Add at least One Sender!!")
        if self .groups_number ==0 :
            self .log_text .setPlainText (f"Groups File is Empty!!"+"\n")
        if self .test_groups_number ==0 :
            self .log_text .setPlainText (f"Test Groups File is Emtpy!!"+"\n")


    def showEvent (self ,event ):
        super ().showEvent (event )
        QTimer .singleShot (0 ,self .getting_started )



    def show_message_box (self ):
        msg_box =QMessageBox ()
        msg_box .setWindowTitle ("Senders Number is Low")
        msg_box .setText ("Senders Number is Low you need more, Do you want to proceed with these numbers?")
        msg_box .setStandardButtons (QMessageBox .StandardButton .Ok |QMessageBox .StandardButton .Cancel )
        result =msg_box .exec ()
        self .return_result_signal .emit (result )



    def handle_returned_result (self ,result ):

        if hasattr (self ,'worker')and self .worker is not None :
            try :
                if result ==QMessageBox .StandardButton .Ok :
                    self .worker .pass_result_to_worker_signal .emit (True )
                else :
                    self .worker .pass_result_to_worker_signal .emit (False )
            except RuntimeError as e :

                if hasattr (self ,'logger'):
                    self .logger .warning (f"Worker object has been deleted, cannot pass result: {str(e)}")
                else :
                    print (f"Warning: Worker object has been deleted, cannot pass result: {str(e)}")
        else :

            if hasattr (self ,'logger'):
                self .logger .warning ("Worker object is None or doesn't exist, cannot pass result")
            else :
                print ("Warning: Worker object is None or doesn't exist, cannot pass result")

    def cleanup_worker (self ):
        """Safely cleanup the worker object and its connections"""
        try :
            if hasattr (self ,'worker')and self .worker is not None :

                try :
                    self .worker .log_message .disconnect ()
                    self .worker .show_message_box_signal .disconnect ()
                    self .worker .all_emails_sent_signal .disconnect ()
                    self .worker .pass_result_to_worker_signal .disconnect ()
                except (RuntimeError ,TypeError ):

                    pass 


                self .worker .deleteLater ()
                self .worker =None 

                if hasattr (self ,'logger'):
                    self .logger .info ("Worker cleanup completed successfully")
        except Exception as e :
            if hasattr (self ,'logger'):
                self .logger .error (f"Error during worker cleanup: {str(e)}")
            else :
                print (f"Error during worker cleanup: {str(e)}")



    def send (self ):
        if self .subject_input .text ()==""or self .from_input .text ()=="":
            QMessageBox .critical (self ,"Error",f"Please Check Your Subject & From")
        else :

            use_proxy =self .proxy_settings .get ('use_proxy',False )
            emails_per_proxy =self .proxy_settings .get ('emails_per_proxy',5 )
            num_threads =self .proxy_settings .get ('num_threads',3 )


            from PyQt6 .QtWidgets import QDialog ,QVBoxLayout ,QHBoxLayout ,QCheckBox ,QLabel ,QSpinBox ,QPushButton 

            options_dialog =QDialog (self )
            options_dialog .setWindowTitle ("Sending Options")
            options_dialog .setFixedSize (400 ,250 )
            layout =QVBoxLayout (options_dialog )


            use_proxy_checkbox =QCheckBox ("Use proxy rotation",options_dialog )
            use_proxy_checkbox .setChecked (use_proxy )
            layout .addWidget (use_proxy_checkbox )


            proxy_change_layout =QHBoxLayout ()
            emails_per_proxy_label =QLabel ("Emails per proxy:",options_dialog )
            proxy_change_layout .addWidget (emails_per_proxy_label )

            emails_per_proxy_spin =QSpinBox (options_dialog )
            emails_per_proxy_spin .setMinimum (1 )
            emails_per_proxy_spin .setMaximum (100 )
            emails_per_proxy_spin .setValue (emails_per_proxy )
            proxy_change_layout .addWidget (emails_per_proxy_spin )
            layout .addLayout (proxy_change_layout )


            thread_layout =QHBoxLayout ()
            thread_label =QLabel ("Number of threads:",options_dialog )
            thread_layout .addWidget (thread_label )

            thread_spin =QSpinBox (options_dialog )
            thread_spin .setMinimum (1 )
            thread_spin .setMaximum (10 )
            thread_spin .setValue (num_threads )
            thread_layout .addWidget (thread_spin )
            layout .addLayout (thread_layout )


            button_layout =QHBoxLayout ()
            ok_button =QPushButton ("Start Sending",options_dialog )
            cancel_button =QPushButton ("Cancel",options_dialog )
            button_layout .addWidget (ok_button )
            button_layout .addWidget (cancel_button )
            layout .addLayout (button_layout )


            ok_button .clicked .connect (options_dialog .accept )
            cancel_button .clicked .connect (options_dialog .reject )


            if options_dialog .exec ()==QDialog .DialogCode .Accepted :
                use_proxy =use_proxy_checkbox .isChecked ()
                emails_per_proxy =emails_per_proxy_spin .value ()
                num_threads =thread_spin .value ()


                self .proxy_settings .update ({
                'use_proxy':use_proxy ,
                'emails_per_proxy':emails_per_proxy ,
                'num_threads':num_threads 
                })
            else :
                return 

            self .save_data_to_json ()
            self .thread =QThread ()
            self .worker =Worker (
            self .messageBox ,
            self .subject_input .text (),
            self .from_input .text (),
            self .limit_spin .value (),
            self .delay_spin .value (),
            self .html_file_name ,
            self .groups_file_name ,
            self .test_mode .isChecked (),
            self .isp_list .currentText (),
            self .senders_file_name ,
            self .rotation_check .isChecked (),
            use_proxy ,
            emails_per_proxy ,
            num_threads ,
            self .proxy_settings .get ('use_global_proxy',False )
            )
            self .worker .log_message .connect (self .update_log_text )
            self .worker .show_message_box_signal .connect (self .show_message_box )
            self .stop_btn .clicked .connect (self .worker .stop_signal .emit )
            self .worker .all_emails_sent_signal .connect (self .show_all_emails_sent_message )
            self .worker .moveToThread (self .thread )
            self .thread .started .connect (self .worker .run )
            self .worker .finished .connect (self .thread .quit )
            self .worker .finished .connect (self .cleanup_worker )
            self .thread .finished .connect (self .thread .deleteLater )
            try :
                self .thread .start ()
            except Exception as e :
                self .logger .error (f"Worker Error >> {str(e)}")
            self .send_btn .setEnabled (False )
            self .thread .finished .connect (
            lambda :self .send_btn .setEnabled (True )
            )



    def update_log_text (self ,message ):
        self .log_text .setPlainText (self .log_text .toPlainText ()+message +"\n")




    def show_all_emails_sent_message (self ):
        QMessageBox .information (self ,"Sender","All Emails Sent successfully.")




    def editfile (self ,file ):
        dialog =QDialog (self )
        dialog .setFixedSize (700 ,500 )
        dialog .setWindowTitle (f"Edit {file}")
        layout =QVBoxLayout (dialog )
        text_editor =QPlainTextEdit (self )
        layout .addWidget (text_editor )
        if file =="Senders":
            file_name =self .senders_file_name 
        elif file =="Groups":
            file_name =self .groups_file_name 
        else :
            file_name =self .groups_test_file_name 
        file_path =f"{home}/{file_name}"
        try :
            with open (file_path ,'r',encoding ='utf-8')as file :
                text_content =file .read ()
                text_editor .setPlainText (text_content )
        except Exception as e :
            self .logger .error (f"Error while opening {file} >> {str(e)}")
        save_button =QPushButton ("Save",dialog )
        layout .addWidget (save_button )
        save_button .clicked .connect (lambda :self .saveEditedFile (text_editor ,file_path ))
        save_button .clicked .connect (lambda :self .close_window (dialog ))
        dialog .exec ()


    def test_mode_check (self ,state ):
        if state ==2 :
            self .log_text .setPlainText (self .log_text .toPlainText ()+"Entering Test Mode!!"+"\n")
            with open (f"{home}/{self.groups_test_file_name}",'r')as test_groups_file :
                self .test_groups_number =len (test_groups_file .readlines ())
                if self .test_groups_number ==0 :
                    QMessageBox .critical (self ,"Error",f"Please add at least one Test email!!")
        else :
            self .log_text .setPlainText (self .log_text .toPlainText ()+"Entering Normal Mode!!"+"\n")


    def prevBtn (self ):
        dialog =QDialog (self )
        dialog .setFixedSize (800 ,700 )
        dialog .setWindowTitle ("HTML Preview")
        layout =QVBoxLayout (dialog )
        web_view =QWebEngineView (dialog )
        layout .addWidget (web_view )
        settings =web_view .settings ()
        settings =web_view .page ().settings ()
        settings .setAttribute (QWebEngineSettings .WebAttribute .LocalContentCanAccessRemoteUrls ,True )
        web_view .setContextMenuPolicy (Qt .ContextMenuPolicy .NoContextMenu )
        file_url =QUrl .fromLocalFile (self .html_file_path )
        web_view .load (file_url )
        dialog .exec ()



    def editBtn (self ):
        dialog =QDialog (self )
        dialog .setFixedSize (800 ,700 )
        dialog .setWindowTitle ("Edit Html")
        layout =QVBoxLayout (dialog )
        html_editor =QPlainTextEdit (self )
        layout .addWidget (html_editor )

        file_path =f"{home}/{self.html_file_name}"
        with open (file_path ,'r',encoding ='utf-8')as file :
            html_content =file .read ()
            html_editor .setPlainText (html_content )

        save_button =QPushButton ("Save",dialog )
        layout .addWidget (save_button )
        save_button .clicked .connect (lambda :self .saveHtmlToFile (html_editor ))
        save_button .clicked .connect (lambda :self .close_window (dialog ))
        dialog .exec ()




    def saveHtmlToFile (self ,html_editor ):
        edited_html =html_editor .toPlainText ()
        try :
            with open (self .html_file_path ,'w',encoding ='utf-8')as file :
                file .write (edited_html )
            QMessageBox .information (html_editor ,"Save HTML","HTML content saved to 'offre.html' successfully.")
        except Exception as e :
            QMessageBox .critical (html_editor ,"Error",f"Failed to save HTML: {str(e)}")




    def saveEditedFile (self ,text_editor ,file_path ):
        edited_text =text_editor .toPlainText ()
        if self .only_groups .isChecked ():
            lines =edited_text .split ("\n")
            cleaned_lines =[line for line in lines if "@googlegroups.com"in line ]
            cleaned_lines =list (dict .fromkeys (cleaned_lines ))
            edited_text ="\n".join (cleaned_lines )
        else :
            lines =edited_text .split ("\n")
            cleaned_lines =list (dict .fromkeys (lines ))
            edited_text ="\n".join (cleaned_lines )

        try :
            with open (file_path ,'w',encoding ='utf-8')as file :
                file .write (edited_text )
            QMessageBox .information (text_editor ,"Save File","File content saved successfully.")
            if "senders"in file_path :
                self .senders_num .setText (str (len (edited_text .split ("\n"))))
            else :
                self .groups_num .setText (str (len (edited_text .split ("\n"))))
        except Exception as e :
            QMessageBox .critical (text_editor ,"Error",f"Failed to save File: {str(e)}")



    def close_window (self ,dialog ):
        dialog .accept ()

    def view_failed_senders (self ):
        """Open the failed senders file in a dialog for viewing and editing"""
        from PyQt6 .QtWidgets import QHBoxLayout 
        dialog =QDialog (self )
        dialog .setFixedSize (800 ,600 )
        dialog .setWindowTitle ("Failed Senders")
        layout =QVBoxLayout (dialog )
        text_editor =QPlainTextEdit (self )

        text_editor .setReadOnly (True )
        layout .addWidget (text_editor )


        import os 
        if not os .path .exists (self .failed_senders_file ):
            with open (self .failed_senders_file ,'w',encoding ='utf-8')as file :
                file .write ("# Failed senders log\n")
                file .write ("# Format: Timestamp | Sender | Error Message\n\n")


        try :
            with open (self .failed_senders_file ,'r',encoding ='utf-8')as file :
                text_content =file .read ()
                text_editor .setPlainText (text_content )
        except Exception as e :
            self .logger .error (f"Error while opening failed senders file >> {str(e)}")
            text_editor .setPlainText (f"Error opening file: {str(e)}")


        button_layout =QHBoxLayout ()

        export_button =QPushButton ("Export Email:Pass",dialog )
        export_button .clicked .connect (lambda :self .export_failed_senders (text_editor .toPlainText ()))

        clear_button =QPushButton ("Clear",dialog )
        clear_button .clicked .connect (lambda :self .clear_failed_senders_file (text_editor ))

        close_button =QPushButton ("Close",dialog )
        close_button .clicked .connect (lambda :self .close_window (dialog ))

        button_layout .addWidget (export_button )
        button_layout .addWidget (clear_button )
        button_layout .addWidget (close_button )

        layout .addLayout (button_layout )
        dialog .exec ()

    def export_failed_senders (self ,content ):
        """Export failed senders in email:pass format"""

        from PyQt6 .QtWidgets import QProgressDialog 
        progress =QProgressDialog ("Exporting failed senders...","Cancel",0 ,100 ,self )
        progress .setWindowTitle ("Export Progress")
        progress .setWindowModality (Qt .WindowModality .WindowModal )
        progress .setMinimumDuration (0 )
        progress .setValue (0 )
        progress .show ()


        class ExportWorker (QObject ):
            finished =pyqtSignal (bool ,str ,int )
            progress_update =pyqtSignal (int )

            def __init__ (self ,content ,senders_file_name ):
                super ().__init__ ()
                self .content =content 
                self .senders_file_name =senders_file_name 

            def run (self ):
                try :

                    unique_emails =set ()
                    self .progress_update .emit (10 )

                    for line in self .content .split ('\n'):
                        if line .startswith ('#')or not line .strip ():
                            continue 

                        parts =line .split (' | ',2 )
                        if len (parts )>=2 :
                            email =parts [1 ].strip ()
                            if '@'in email :
                                unique_emails .add (email )

                    if not unique_emails :
                        self .finished .emit (False ,"No email addresses found to export.",0 )
                        return 

                    self .progress_update .emit (30 )


                    sender_credentials ={}
                    try :
                        with open (self .senders_file_name ,'r')as senders_file :
                            for line in senders_file :
                                line =line .strip ()
                                if not line :
                                    continue 


                                if ';'in line :
                                    email ,password =line .split (';',1 )
                                    sender_credentials [email .strip ()]=password .strip ()
                                elif ':'in line :
                                    email ,password =line .split (':',1 )
                                    sender_credentials [email .strip ()]=password .strip ()
                    except Exception as e :
                        self .finished .emit (False ,f"Error reading senders file: {str(e)}",0 )
                        return 

                    self .progress_update .emit (60 )


                    export_file_path =f"{home}/failed_senders_export.txt"
                    with open (export_file_path ,'w',encoding ='utf-8')as export_file :
                        for email in sorted (unique_emails ):
                            if email in sender_credentials :
                                export_file .write (f"{email}:{sender_credentials[email]}\n")
                            else :
                                export_file .write (f"{email}:password_not_found\n")

                    self .progress_update .emit (100 )

                    success_message =f"Failed senders exported to:\n{export_file_path}\n\nTotal emails exported: {len(unique_emails)}"
                    self .finished .emit (True ,success_message ,len (unique_emails ))

                except Exception as e :
                    self .finished .emit (False ,f"Failed to export senders: {str(e)}",0 )


        self .export_thread =QThread ()
        self .export_worker =ExportWorker (content ,f"{home}/{self.senders_file_name}")


        self .export_worker .progress_update .connect (progress .setValue )
        self .export_worker .finished .connect (
        lambda success ,message ,count :self .handle_export_finished (success ,message ,count ,progress )
        )
        progress .canceled .connect (self .export_thread .quit )


        self .export_worker .moveToThread (self .export_thread )
        self .export_thread .started .connect (self .export_worker .run )
        self .export_worker .finished .connect (self .export_thread .quit )
        self .export_worker .finished .connect (self .export_worker .deleteLater )
        self .export_thread .finished .connect (self .export_thread .deleteLater )


        self .export_thread .start ()

    def handle_export_finished (self ,success ,message ,_ ,progress_dialog ):
        """Handle the completion of the export process"""
        progress_dialog .close ()


        if success :
            QMessageBox .information (self ,"Export Successful",message )
        else :
            QMessageBox .critical (self ,"Export Error",message )

    def clear_failed_senders_file (self ,text_editor ):
        """Clear the failed senders file and reset it with headers"""
        try :

            text_editor .setPlainText ("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")


            with open (self .failed_senders_file ,'w',encoding ='utf-8')as file :
                file .write ("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")

            QMessageBox .information (text_editor ,"Clear File","Failed senders file cleared successfully.")
        except Exception as e :
            QMessageBox .critical (text_editor ,"Error",f"Failed to clear file: {str(e)}")

    def health_check_senders (self ):
        """Test SMTP login for all senders without sending emails"""

        dialog =QDialog (self )
        dialog .setFixedSize (800 ,600 )
        dialog .setWindowTitle ("Sender Health Check")
        layout =QVBoxLayout (dialog )


        proxy_group =QGroupBox ("Proxy Settings")
        proxy_layout =QVBoxLayout (proxy_group )


        use_proxy_checkbox =QCheckBox ("Use proxy rotation for health checks")
        use_proxy_checkbox .setChecked (self .proxy_settings .get ('use_proxy',False ))
        use_proxy_checkbox .setToolTip ("Enable proxy rotation to avoid IP-based rate limiting during health checks")
        proxy_layout .addWidget (use_proxy_checkbox )


        proxy_change_layout =QHBoxLayout ()
        proxy_change_label =QLabel ("Checks per proxy:")
        proxy_change_layout .addWidget (proxy_change_label )

        emails_per_proxy_spin =QSpinBox ()
        emails_per_proxy_spin .setMinimum (1 )
        emails_per_proxy_spin .setMaximum (50 )
        emails_per_proxy_spin .setValue (self .proxy_settings .get ('emails_per_proxy',5 ))
        emails_per_proxy_spin .setToolTip ("Number of health checks to perform before rotating to the next proxy")
        proxy_change_layout .addWidget (emails_per_proxy_spin )

        proxy_change_layout .addStretch ()
        proxy_layout .addLayout (proxy_change_layout )


        use_multithreading_checkbox =QCheckBox ("Use multi-threading for faster health checks")
        use_multithreading_checkbox .setChecked (self .proxy_settings .get ('use_multithreading',True ))
        use_multithreading_checkbox .setToolTip ("Test multiple senders simultaneously for much faster results (recommended)")
        proxy_layout .addWidget (use_multithreading_checkbox )

        layout .addWidget (proxy_group )


        results_text =QPlainTextEdit (self )
        results_text .setReadOnly (True )
        layout .addWidget (results_text )


        button_layout =QHBoxLayout ()

        start_button =QPushButton ("Start Check",dialog )
        cancel_button =QPushButton ("Cancel",dialog )
        cancel_button .setEnabled (False )
        close_button =QPushButton ("Close",dialog )
        close_button .clicked .connect (lambda :self .close_window (dialog ))

        button_layout .addWidget (start_button )
        button_layout .addWidget (cancel_button )
        button_layout .addWidget (close_button )
        layout .addLayout (button_layout )


        dialog .show ()


        def start_health_check ():

            results_text .clear ()


            use_proxy =use_proxy_checkbox .isChecked ()
            emails_per_proxy =emails_per_proxy_spin .value ()
            use_multithreading =use_multithreading_checkbox .isChecked ()


            self .proxy_settings .update ({
            'use_proxy':use_proxy ,
            'emails_per_proxy':emails_per_proxy ,
            'use_multithreading':use_multithreading 
            })


            start_button .setEnabled (False )
            cancel_button .setEnabled (True )


            self .health_check_thread =QThread ()
            self .health_check_worker =HealthCheckWorker (
            f"{home}/{self.senders_file_name}",
            self .failed_senders_file ,
            use_proxy =use_proxy ,
            emails_per_proxy =emails_per_proxy ,
            use_multithreading =use_multithreading ,
            use_global_proxy =self .proxy_settings .get ('use_global_proxy',False )
            )


            self .health_check_worker .progress_update .connect (
            lambda msg :self .update_health_check_results (results_text ,msg )
            )
            cancel_button .clicked .connect (self .health_check_worker .cancel_check )


            self .health_check_worker .moveToThread (self .health_check_thread )
            self .health_check_thread .started .connect (self .health_check_worker .run )
            self .health_check_worker .finished .connect (self .health_check_thread .quit )
            self .health_check_worker .finished .connect (self .health_check_worker .deleteLater )
            self .health_check_thread .finished .connect (self .health_check_thread .deleteLater )
            self .health_check_thread .finished .connect (
            lambda :cancel_button .setEnabled (False )
            )
            self .health_check_thread .finished .connect (
            lambda :start_button .setEnabled (True )
            )



            self .health_check_thread .start ()


        start_button .clicked .connect (start_health_check )


        dialog .exec ()


        try :
            if hasattr (self ,'health_check_thread')and self .health_check_thread is not None :

                if hasattr (self .health_check_thread ,'isRunning')and callable (self .health_check_thread .isRunning ):
                    if self .health_check_thread .isRunning ():
                        if hasattr (self ,'health_check_worker')and self .health_check_worker is not None :
                            self .health_check_worker .cancel_check ()
                        self .health_check_thread .quit ()

                        if not self .health_check_thread .wait (3000 ):
                            self .logger .warning ("Health check thread did not terminate within timeout")
                else :
                    self .logger .warning (f"Health check thread object is not a valid QThread: {type(self.health_check_thread)}")

                self .health_check_thread =None 
                self .health_check_worker =None 
        except Exception as e :
            self .logger .error (f"Error cleaning up health check thread: {str(e)}")

            self .health_check_thread =None 
            self .health_check_worker =None 

    def update_health_check_results (self ,text_widget ,message ):
        """Update the health check results text widget"""
        text_widget .setPlainText (text_widget .toPlainText ()+message +"\n")

        text_widget .verticalScrollBar ().setValue (text_widget .verticalScrollBar ().maximum ())

    def configure_proxies (self ):
        """Open proxy configuration dialog"""
        from PyQt6 .QtWidgets import (QDialog ,QVBoxLayout ,QHBoxLayout ,QLabel ,
        QTextEdit ,QPushButton ,QSpinBox ,QCheckBox ,
        QTabWidget ,QWidget ,QListWidget ,QListWidgetItem ,
        QLineEdit ,QComboBox ,QMessageBox ,QProgressDialog )

        dialog =QDialog (self )
        dialog .setWindowTitle ("Proxy Configuration")
        dialog .setFixedSize (600 ,500 )
        layout =QVBoxLayout (dialog )


        tab_widget =QTabWidget ()
        layout .addWidget (tab_widget )


        proxy_tab =QWidget ()
        proxy_layout =QVBoxLayout (proxy_tab )


        instructions =QLabel ("Enter proxy configurations (one per line):\nFormat: protocol://host:port:username:password\nExample: socks5://proxy.example.com:1080:user:pass")
        instructions .setWordWrap (True )
        proxy_layout .addWidget (instructions )


        proxy_text =QTextEdit ()
        proxy_text .setPlaceholderText ("socks5://proxy1.example.com:1080:user:pass\nhttp://proxy2.example.com:8080:user:pass")


        try :
            with open (f"{home}/proxies.txt",'r')as f :
                proxy_text .setPlainText (f .read ())
        except FileNotFoundError :
            pass 

        proxy_layout .addWidget (proxy_text )


        proxy_buttons =QHBoxLayout ()

        load_btn =QPushButton ("Load from File")
        save_btn =QPushButton ("Save to File")
        test_btn =QPushButton ("Test Proxies")

        proxy_buttons .addWidget (load_btn )
        proxy_buttons .addWidget (save_btn )
        proxy_buttons .addWidget (test_btn )
        proxy_layout .addLayout (proxy_buttons )

        tab_widget .addTab (proxy_tab ,"Proxy List")


        settings_tab =QWidget ()
        settings_layout =QVBoxLayout (settings_tab )


        rotation_group =QVBoxLayout ()

        use_proxy_checkbox =QCheckBox ("Enable proxy rotation")
        use_proxy_checkbox .setChecked (True )
        rotation_group .addWidget (use_proxy_checkbox )


        global_proxy_checkbox =QCheckBox ("Use global proxy mode (monkey-patch all sockets)")
        global_proxy_checkbox .setChecked (self .proxy_settings .get ('use_global_proxy',False ))
        global_proxy_checkbox .setToolTip ("Enable global socket monkey-patching for maximum compatibility.\nThis affects ALL network connections in the application.")
        rotation_group .addWidget (global_proxy_checkbox )

        emails_per_proxy_layout =QHBoxLayout ()
        emails_per_proxy_label =QLabel ("Emails per proxy:")
        emails_per_proxy_spin =QSpinBox ()
        emails_per_proxy_spin .setMinimum (1 )
        emails_per_proxy_spin .setMaximum (100 )
        emails_per_proxy_spin .setValue (5 )
        emails_per_proxy_layout .addWidget (emails_per_proxy_label )
        emails_per_proxy_layout .addWidget (emails_per_proxy_spin )
        rotation_group .addLayout (emails_per_proxy_layout )


        thread_layout =QHBoxLayout ()
        thread_label =QLabel ("Number of threads:")
        thread_spin =QSpinBox ()
        thread_spin .setMinimum (1 )
        thread_spin .setMaximum (10 )
        thread_spin .setValue (3 )
        thread_layout .addWidget (thread_label )
        thread_layout .addWidget (thread_spin )
        rotation_group .addLayout (thread_layout )

        settings_layout .addLayout (rotation_group )
        settings_layout .addStretch ()

        tab_widget .addTab (settings_tab ,"Settings")


        button_layout =QHBoxLayout ()
        ok_button =QPushButton ("OK")
        cancel_button =QPushButton ("Cancel")
        button_layout .addWidget (ok_button )
        button_layout .addWidget (cancel_button )
        layout .addLayout (button_layout )


        def save_proxies ():
            try :
                with open (f"{home}/proxies.txt",'w')as f :
                    f .write (proxy_text .toPlainText ())
                QMessageBox .information (dialog ,"Success","Proxies saved successfully!")
            except Exception as e :
                QMessageBox .critical (dialog ,"Error",f"Failed to save proxies: {str(e)}")

        def load_proxies ():
            try :
                from PyQt6 .QtWidgets import QFileDialog 
                filename ,_ =QFileDialog .getOpenFileName (dialog ,"Load Proxy File","","Text Files (*.txt)")
                if filename :
                    with open (filename ,'r')as f :
                        proxy_text .setPlainText (f .read ())
            except Exception as e :
                QMessageBox .critical (dialog ,"Error",f"Failed to load proxies: {str(e)}")

        def test_proxies ():
            proxy_manager =ProxyManager (max_concurrent_connections =4 )
            proxy_lines =proxy_text .toPlainText ().strip ().split ('\n')

            if not proxy_lines or not proxy_lines [0 ]:
                QMessageBox .warning (dialog ,"Warning","No proxies to test!")
                return 


            proxy_configs =[]
            for line in proxy_lines :
                line =line .strip ()
                if line and not line .startswith ('#'):
                    proxy_config =proxy_manager .parse_proxy_string (line )
                    if proxy_config :
                        proxy_configs .append (proxy_config )

            if not proxy_configs :
                QMessageBox .warning (dialog ,"Warning","No valid proxies found!")
                return 


            progress =QProgressDialog ("Testing proxies in parallel...","Cancel",0 ,len (proxy_configs ),dialog )
            progress .setWindowModality (Qt .WindowModality .WindowModal )
            progress .show ()


            test_results =proxy_manager .test_proxies_parallel (proxy_configs ,max_workers =15 ,timeout =3 )

            working_count =0 
            results =[]

            for proxy_config ,is_working in test_results :
                if progress .wasCanceled ():
                    break 

                if is_working :
                    working_count +=1 
                    results .append (f" {proxy_config['host']}:{proxy_config['port']} - Working")
                else :
                    results .append (f" {proxy_config['host']}:{proxy_config['port']} - Failed")

                progress .setValue (len (results ))

            progress .close ()


            result_dialog =QDialog (dialog )
            result_dialog .setWindowTitle ("Proxy Test Results")
            result_dialog .setFixedSize (500 ,400 )
            result_layout =QVBoxLayout (result_dialog )

            result_text =QTextEdit ()
            result_text .setReadOnly (True )
            result_text .setPlainText (f"Working proxies: {working_count}/{len([l for l in proxy_lines if l.strip() and not l.startswith('#')])}\n\n"+"\n".join (results ))
            result_layout .addWidget (result_text )

            close_btn =QPushButton ("Close")
            close_btn .clicked .connect (result_dialog .accept )
            result_layout .addWidget (close_btn )

            result_dialog .exec ()

        load_btn .clicked .connect (load_proxies )
        save_btn .clicked .connect (save_proxies )
        test_btn .clicked .connect (test_proxies )
        ok_button .clicked .connect (dialog .accept )
        cancel_button .clicked .connect (dialog .reject )


        if dialog .exec ()==QDialog .DialogCode .Accepted :

            save_proxies ()


            try :
                config_data ={
                'use_proxy':use_proxy_checkbox .isChecked (),
                'emails_per_proxy':emails_per_proxy_spin .value (),
                'num_threads':thread_spin .value (),
                'use_global_proxy':global_proxy_checkbox .isChecked ()
                }


                try :
                    with open (self .saved_data_js ,'r')as f :
                        existing_data =json .load (f )
                except FileNotFoundError :
                    existing_data ={}

                existing_data .update (config_data )

                with open (self .saved_data_js ,'w')as f :
                    json .dump (existing_data ,f )

                QMessageBox .information (self ,"Success","Proxy configuration saved successfully!")

            except Exception as e :
                QMessageBox .critical (self ,"Error",f"Failed to save configuration: {str(e)}")

    def load_saved_data (self ):
        try :
            with open (self .saved_data_js ,'r')as file :
                data =json .load (file )
                self .subject_input .setText (data .get ('subject',''))
                self .from_input .setText (data .get ('from',''))
                self .limit_spin .setValue (data .get ('limit',150 ))
                self .delay_spin .setValue (data .get ('delay',49 ))
                self .only_groups .setChecked (data .get ('only_groups',False ))
                self .isp_list .setCurrentText (data .get ('isp','Gmail'))
                if 'rotation'in data :
                    self .rotation_check .setChecked (data ['rotation'])


                self .proxy_settings ={
                'use_proxy':data .get ('use_proxy',False ),
                'emails_per_proxy':data .get ('emails_per_proxy',5 ),
                'num_threads':data .get ('num_threads',3 ),
                'use_global_proxy':data .get ('use_global_proxy',False )
                }
        except FileNotFoundError :

            self .proxy_settings ={
            'use_proxy':False ,
            'emails_per_proxy':5 ,
            'num_threads':3 
            }



    def save_data_to_json (self ):
        data ={
        'subject':self .subject_input .text (),
        'from':self .from_input .text (),
        'limit':self .limit_spin .value (),
        'delay':self .delay_spin .value (),
        'only_groups':self .only_groups .isChecked (),
        'isp':self .isp_list .currentText (),
        'rotation':self .rotation_check .isChecked (),
        }


        if hasattr (self ,'proxy_settings'):
            data .update (self .proxy_settings )

        with open (self .saved_data_js ,'w')as file :
            json .dump (data ,file )

    def closeEvent (self ,event ):
        """Handle application close event - clean up threads"""
        self .logger .info ("Application closing - cleaning up threads")


        try :
            if hasattr (self ,'health_check_thread')and self .health_check_thread is not None :

                if hasattr (self .health_check_thread ,'isRunning')and callable (self .health_check_thread .isRunning ):
                    if self .health_check_thread .isRunning ():
                        self .logger .info ("Stopping health check thread")
                        if hasattr (self ,'health_check_worker')and self .health_check_worker is not None :
                            self .health_check_worker .cancel_check ()
                        self .health_check_thread .quit ()

                        if not self .health_check_thread .wait (2000 ):
                            self .logger .warning ("Health check thread did not terminate within timeout")
                else :
                    self .logger .warning (f"Health check thread object is not a valid QThread: {type(self.health_check_thread)}")
                self .health_check_thread =None 
                self .health_check_worker =None 
        except Exception as e :
            self .logger .error (f"Error cleaning up health check thread: {str(e)}")

            self .health_check_thread =None 
            self .health_check_worker =None 


        try :
            if hasattr (self ,'thread')and self .thread is not None :

                if hasattr (self .thread ,'isRunning')and callable (self .thread .isRunning ):
                    if self .thread .isRunning ():
                        self .logger .info ("Stopping email sending thread")
                        if hasattr (self ,'worker')and self .worker is not None :
                            self .worker .stop_signal .emit ()
                        self .thread .quit ()

                        if not self .thread .wait (2000 ):
                            self .logger .warning ("Email sending thread did not terminate within timeout")
                else :
                    self .logger .warning (f"Thread object is not a valid QThread: {type(self.thread)}")


                self .cleanup_worker ()
                self .thread =None 
        except Exception as e :
            self .logger .error (f"Error cleaning up email sending thread: {str(e)}")

            try :
                self .cleanup_worker ()
            except :
                pass 
            self .thread =None 


        try :
            if hasattr (self ,'export_thread')and self .export_thread is not None :

                if hasattr (self .export_thread ,'isRunning')and callable (self .export_thread .isRunning ):
                    if self .export_thread .isRunning ():
                        self .logger .info ("Stopping export thread")
                        self .export_thread .quit ()

                        if not self .export_thread .wait (2000 ):
                            self .logger .warning ("Export thread did not terminate within timeout")
                else :
                    self .logger .warning (f"Export thread object is not a valid QThread: {type(self.export_thread)}")
                self .export_thread =None 
                self .export_worker =None 
        except Exception as e :
            self .logger .error (f"Error cleaning up export thread: {str(e)}")

            self .export_thread =None 
            self .export_worker =None 


        try :
            self .save_data_to_json ()
        except Exception as e :
            self .logger .error (f"Error saving data: {str(e)}")


        event .accept ()


def initialize_com ():
    """Initialize COM for the main thread"""
    try :
        import pythoncom 
        pythoncom .CoInitialize ()
        print ("COM initialized for main thread")
        return True 
    except ImportError :
        print ("pythoncom not available")
        return False 
    except Exception as e :
        print (f"Failed to initialize COM library ({str(e)})")
        return False 

if __name__ =="__main__":

    initialize_com ()

    print ("Starting>>>")


    app =QApplication (sys .argv )
    window =MainWindow ()

    app_icon =QIcon ()
    app_icon .addFile (f'{home}/img/Groups_16.png',QtCore .QSize (16 ,16 ))
    app_icon .addFile (f'{home}/img/Groups_24.png',QtCore .QSize (24 ,24 ))
    app_icon .addFile (f'{home}/img/Groups_32.png',QtCore .QSize (32 ,32 ))
    app_icon .addFile (f'{home}/img/Groups_48.png',QtCore .QSize (48 ,48 ))
    app_icon .addFile (f'{home}/img/Groups_256.png',QtCore .QSize (256 ,256 ))
    window .setWindowIcon (app_icon )
    window .show ()
    app .exec ()