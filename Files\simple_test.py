#!/usr/bin/env python3
"""
Simple test to verify the proxy improvements work.
"""

import threading
import time

# Mock ProxyManager for testing without full dependencies
class MockProxyManager:
    def __init__(self):
        self.proxies = []
        self.thread_proxy_assignments = {}
        self.proxy_assignment_lock = threading.Lock()
        self.next_proxy_index = 0
        self.proxy_blacklist = set()
        self.proxy_failure_counts = {}
        self.proxy_last_failure_time = {}
        self.blacklist_duration = 300
        
    def add_proxy(self, proxy_str):
        # Simple parsing for test
        parts = proxy_str.split('://')
        if len(parts) == 2:
            protocol = parts[0]
            rest = parts[1].split(':')
            if len(rest) >= 4:
                host, port, username, password = rest[0], int(rest[1]), rest[2], rest[3]
                proxy = {
                    'protocol': protocol,
                    'host': host,
                    'port': port,
                    'username': username,
                    'password': password
                }
                self.proxies.append(proxy)
                print(f"Added proxy: {host}:{port}")
                return True
        return False
    
    def _get_proxy_key(self, proxy_config):
        return f"{proxy_config['host']}:{proxy_config['port']}"
    
    def _get_available_proxies(self):
        available = []
        for i, proxy in enumerate(self.proxies):
            proxy_key = self._get_proxy_key(proxy)
            if proxy_key not in self.proxy_blacklist:
                available.append(i)
        return available
    
    def get_proxy_for_thread(self, thread_id=None):
        if thread_id is None:
            thread_id = threading.current_thread().ident
            
        with self.proxy_assignment_lock:
            if not self.proxies:
                return None
                
            available_proxies = self._get_available_proxies()
            if not available_proxies:
                available_proxies = list(range(len(self.proxies)))
            
            # Check if thread already has assignment
            if thread_id in self.thread_proxy_assignments:
                assigned_index = self.thread_proxy_assignments[thread_id]
                if assigned_index in available_proxies:
                    return self.proxies[assigned_index]
                else:
                    del self.thread_proxy_assignments[thread_id]
            
            # Assign new proxy
            if available_proxies:
                attempts = 0
                while attempts < len(available_proxies):
                    proxy_index = available_proxies[self.next_proxy_index % len(available_proxies)]
                    self.next_proxy_index += 1
                    
                    if proxy_index not in self.thread_proxy_assignments.values():
                        self.thread_proxy_assignments[thread_id] = proxy_index
                        proxy = self.proxies[proxy_index]
                        print(f"Assigned proxy {proxy['host']}:{proxy['port']} to thread {thread_id}")
                        return proxy
                    attempts += 1
                
                # Fallback to shared assignment
                proxy_index = available_proxies[self.next_proxy_index % len(available_proxies)]
                self.next_proxy_index += 1
                self.thread_proxy_assignments[thread_id] = proxy_index
                proxy = self.proxies[proxy_index]
                print(f"Assigned shared proxy {proxy['host']}:{proxy['port']} to thread {thread_id}")
                return proxy
                
        return None
    
    def rotate_proxy(self, thread_id=None):
        if thread_id is None:
            thread_id = threading.current_thread().ident
            
        # Remove current assignment
        with self.proxy_assignment_lock:
            if thread_id in self.thread_proxy_assignments:
                old_proxy_index = self.thread_proxy_assignments[thread_id]
                old_proxy = self.proxies[old_proxy_index]
                del self.thread_proxy_assignments[thread_id]
                print(f"Thread {thread_id} rotating away from proxy {old_proxy['host']}:{old_proxy['port']}")
        
        # Get new assignment
        new_proxy = self.get_proxy_for_thread(thread_id)
        if new_proxy:
            print(f"Thread {thread_id} rotated to proxy {new_proxy['host']}:{new_proxy['port']}")
        return new_proxy

def test_thread_safe_distribution():
    print("=== Testing Thread-Safe Proxy Distribution ===")
    
    proxy_manager = MockProxyManager()
    
    # Add test proxies
    test_proxies = [
        "socks5://proxy1.test:1080:user:pass",
        "socks5://proxy2.test:1080:user:pass", 
        "socks5://proxy3.test:1080:user:pass"
    ]
    
    for proxy_str in test_proxies:
        proxy_manager.add_proxy(proxy_str)
    
    print(f"Added {len(proxy_manager.proxies)} test proxies")
    
    # Test thread assignments
    thread_assignments = {}
    
    def get_proxy_for_thread(thread_id):
        proxy = proxy_manager.get_proxy_for_thread(thread_id)
        if proxy:
            thread_assignments[thread_id] = f"{proxy['host']}:{proxy['port']}"
        else:
            thread_assignments[thread_id] = None
    
    # Test with multiple threads
    threads = []
    for i in range(5):
        thread = threading.Thread(target=get_proxy_for_thread, args=(i,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"Thread assignments: {thread_assignments}")
    
    # Test proxy rotation
    print("\n--- Testing Proxy Rotation ---")
    original_proxy = proxy_manager.get_proxy_for_thread(999)
    if original_proxy:
        print(f"Thread 999 original proxy: {original_proxy['host']}:{original_proxy['port']}")
        
        rotated_proxy = proxy_manager.rotate_proxy(999)
        if rotated_proxy:
            print(f"Thread 999 rotated proxy: {rotated_proxy['host']}:{rotated_proxy['port']}")
            
            if original_proxy != rotated_proxy:
                print("✓ Proxy rotation working correctly")
            else:
                print("✗ Proxy rotation may not be working")
        else:
            print("✗ Failed to rotate proxy")
    else:
        print("✗ Failed to get original proxy")

if __name__ == "__main__":
    print("Testing Proxy System Improvements (Simple Test)")
    print("=" * 50)
    
    try:
        test_thread_safe_distribution()
        print("\n" + "=" * 50)
        print("Simple test completed successfully!")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
