#!/usr/bin/env python3
"""
Test script to verify the proxy improvements work correctly.
This script tests the new thread-safe proxy distribution and blacklisting features.
"""

import sys
import os
import time
import threading
import logging

# Add the Files directory to the path so we can import smtp
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smtp import ProxyManager

def test_thread_safe_proxy_distribution():
    """Test that different threads get different proxies."""
    print("=== Testing Thread-Safe Proxy Distribution ===")
    
    # Create proxy manager
    proxy_manager = ProxyManager(max_concurrent_connections=2)
    
    # Add test proxies (these won't actually connect, just test the logic)
    test_proxies = [
        "socks5://proxy1.test:1080:user:pass",
        "socks5://proxy2.test:1080:user:pass", 
        "socks5://proxy3.test:1080:user:pass",
        "socks5://proxy4.test:1080:user:pass",
        "socks5://proxy5.test:1080:user:pass"
    ]
    
    for proxy_str in test_proxies:
        proxy_manager.add_proxy(proxy_str)
    
    print(f"Added {len(proxy_manager.proxies)} test proxies")
    
    # Test thread assignments
    thread_assignments = {}
    
    def get_proxy_for_thread(thread_id):
        proxy = proxy_manager.get_proxy_for_thread(thread_id)
        if proxy:
            thread_assignments[thread_id] = f"{proxy['host']}:{proxy['port']}"
            print(f"Thread {thread_id} assigned: {proxy['host']}:{proxy['port']}")
        else:
            thread_assignments[thread_id] = None
            print(f"Thread {thread_id} assigned: None")
    
    # Test with multiple threads
    threads = []
    for i in range(8):  # More threads than proxies to test sharing
        thread = threading.Thread(target=get_proxy_for_thread, args=(i,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    # Verify different threads got different proxies (when possible)
    unique_assignments = set(v for v in thread_assignments.values() if v is not None)
    print(f"Unique proxy assignments: {len(unique_assignments)}")
    print(f"Thread assignments: {thread_assignments}")
    
    # Test proxy rotation
    print("\n--- Testing Proxy Rotation ---")
    original_proxy = proxy_manager.get_proxy_for_thread(999)
    print(f"Thread 999 original proxy: {original_proxy['host']}:{original_proxy['port']}")
    
    rotated_proxy = proxy_manager.rotate_proxy(999)
    print(f"Thread 999 rotated proxy: {rotated_proxy['host']}:{rotated_proxy['port']}")
    
    if original_proxy != rotated_proxy:
        print("✓ Proxy rotation working correctly")
    else:
        print("✗ Proxy rotation may not be working")

def test_proxy_blacklisting():
    """Test proxy blacklisting functionality."""
    print("\n=== Testing Proxy Blacklisting ===")
    
    proxy_manager = ProxyManager()
    
    # Add test proxies
    test_proxies = [
        "socks5://good.proxy:1080:user:pass",
        "socks5://bad.proxy:1080:user:pass"
    ]
    
    for proxy_str in test_proxies:
        proxy_manager.add_proxy(proxy_str)
    
    print(f"Added {len(proxy_manager.proxies)} test proxies")
    
    # Test blacklisting
    bad_proxy = proxy_manager.proxies[1]  # bad.proxy
    print(f"Blacklisting proxy: {bad_proxy['host']}:{bad_proxy['port']}")
    
    # Simulate failures to trigger blacklisting
    for i in range(3):  # Should trigger blacklist after 3 failures
        proxy_manager.blacklist_proxy(bad_proxy, f"Test failure {i+1}")
    
    # Check if proxy is blacklisted
    is_blacklisted = proxy_manager.is_proxy_blacklisted(bad_proxy)
    print(f"Proxy blacklisted: {is_blacklisted}")
    
    if is_blacklisted:
        print("✓ Proxy blacklisting working correctly")
    else:
        print("✗ Proxy blacklisting may not be working")
    
    # Test that blacklisted proxy is not assigned to threads
    available_proxies = proxy_manager._get_available_proxies()
    print(f"Available (non-blacklisted) proxies: {len(available_proxies)}")
    
    # Get proxy for thread - should not get the blacklisted one
    assigned_proxy = proxy_manager.get_proxy_for_thread(1001)
    if assigned_proxy:
        assigned_key = proxy_manager._get_proxy_key(assigned_proxy)
        blacklisted_key = proxy_manager._get_proxy_key(bad_proxy)
        
        if assigned_key != blacklisted_key:
            print("✓ Blacklisted proxy correctly avoided in assignment")
        else:
            print("✗ Blacklisted proxy was assigned to thread")
    
    # Test success recording (should reset failure count)
    print("\n--- Testing Success Recording ---")
    proxy_manager.record_proxy_success(bad_proxy)
    failure_count = proxy_manager.proxy_failure_counts.get(
        proxy_manager._get_proxy_key(bad_proxy), 0
    )
    print(f"Failure count after success: {failure_count}")
    
    if failure_count == 0:
        print("✓ Success recording resets failure count")
    else:
        print("✗ Success recording may not be working")

def test_adaptive_delays_and_timeouts():
    """Test adaptive delays and timeouts."""
    print("\n=== Testing Adaptive Delays and Timeouts ===")
    
    proxy_manager = ProxyManager()
    proxy_manager.add_proxy("socks5://test.proxy:1080:user:pass")
    
    test_proxy = proxy_manager.proxies[0]
    
    # Test base timeout
    base_timeout = proxy_manager.get_adaptive_timeout(test_proxy)
    print(f"Base timeout: {base_timeout}s")
    
    # Simulate failures to increase timeout
    for i in range(3):
        proxy_manager.blacklist_proxy(test_proxy, f"Timeout test {i+1}")
    
    adaptive_timeout = proxy_manager.get_adaptive_timeout(test_proxy)
    print(f"Adaptive timeout after failures: {adaptive_timeout}s")
    
    if adaptive_timeout > base_timeout:
        print("✓ Adaptive timeout increases with failures")
    else:
        print("✗ Adaptive timeout may not be working")
    
    # Test connection delay
    print("\n--- Testing Connection Delays ---")
    start_time = time.time()
    proxy_manager._apply_connection_delay(test_proxy, min_delay=1.0)
    first_delay = time.time() - start_time
    print(f"First connection delay: {first_delay:.2f}s")
    
    # Second connection should have adaptive delay
    start_time = time.time()
    proxy_manager._apply_connection_delay(test_proxy, min_delay=1.0)
    second_delay = time.time() - start_time
    print(f"Second connection delay: {second_delay:.2f}s")
    
    if second_delay >= 1.0:
        print("✓ Connection delays working correctly")
    else:
        print("✗ Connection delays may not be working")

def test_statistics_and_monitoring():
    """Test statistics and monitoring features."""
    print("\n=== Testing Statistics and Monitoring ===")
    
    proxy_manager = ProxyManager()
    
    # Add test proxies
    for i in range(3):
        proxy_manager.add_proxy(f"socks5://proxy{i}.test:1080:user:pass")
    
    # Assign proxies to threads
    for thread_id in range(5):
        proxy_manager.get_proxy_for_thread(thread_id)
    
    # Blacklist one proxy
    proxy_manager.blacklist_proxy(proxy_manager.proxies[1], "Test blacklist")
    
    # Get statistics
    stats = proxy_manager.get_proxy_statistics()
    print(f"Total proxies: {stats['total_proxies']}")
    print(f"Blacklisted proxies: {stats['blacklisted_proxies']}")
    print(f"Active threads: {stats['active_threads']}")
    print(f"Thread assignments: {len(stats['thread_assignments'])}")
    
    # Test logging
    print("\n--- Testing Statistics Logging ---")
    proxy_manager.log_proxy_statistics()
    
    print("✓ Statistics and monitoring features working")

if __name__ == "__main__":
    # Set up logging to see debug output
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("Testing Proxy System Improvements")
    print("=" * 50)
    
    try:
        test_thread_safe_proxy_distribution()
        test_proxy_blacklisting()
        test_adaptive_delays_and_timeouts()
        test_statistics_and_monitoring()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("\nKey improvements implemented:")
        print("✓ Thread-safe proxy distribution")
        print("✓ Automatic proxy blacklisting")
        print("✓ TTL expired error handling")
        print("✓ Adaptive delays and timeouts")
        print("✓ Comprehensive logging and monitoring")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
